// Type declarations for Three.js addons
declare module 'three/addons/postprocessing/EffectComposer.js' {
  export { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
}

declare module 'three/addons/postprocessing/RenderPass.js' {
  export { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
}

declare module 'three/addons/postprocessing/UnrealBloomPass.js' {
  export { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
}

// Also declare the jsm versions for compatibility
declare module 'three/examples/jsm/postprocessing/EffectComposer.js' {
  import { WebGLRenderer, Scene, Camera } from 'three';
  
  export class EffectComposer {
    constructor(renderer: WebGLRenderer, renderTarget?: any);
    addPass(pass: any): void;
    insertPass(pass: any, index: number): void;
    removePass(pass: any): void;
    render(deltaTime?: number): void;
    setSize(width: number, height: number): void;
    setPixelRatio(pixelRatio: number): void;
    dispose(): void;
  }
}

declare module 'three/examples/jsm/postprocessing/RenderPass.js' {
  import { Scene, Camera, Material, Color } from 'three';
  
  export class RenderPass {
    constructor(scene: Scene, camera: Camera, overrideMaterial?: Material, clearColor?: Color, clearAlpha?: number);
    render(renderer: any, writeBuffer: any, readBuffer: any, deltaTime: number, maskActive: boolean): void;
  }
}

declare module 'three/examples/jsm/postprocessing/UnrealBloomPass.js' {
  import { Vector2 } from 'three';
  
  export class UnrealBloomPass {
    constructor(resolution: Vector2, strength: number, radius: number, threshold: number);
    render(renderer: any, writeBuffer: any, readBuffer: any, deltaTime: number, maskActive: boolean): void;
    threshold: number;
    strength: number;
    radius: number;
  }
}
