{"version": 3, "file": "ScrollSmoother.min.js", "sources": ["../src/ScrollSmoother.js"], "sourcesContent": ["/*!\n * ScrollSmoother 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _win, _doc, _docEl, _body, _root, _to<PERSON><PERSON>y, _clamp, ScrollTrigger, _mainInstance, _expo, _getVelocityProp, _inputObserver, _context, _onResizeDelayedCall,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_bonusValidated = 1, //<name>ScrollSmoother</name>\n\t_round = value => Math.round(value * 100000) / 100000 || 0,\n\t_maxScroll = scroller => ScrollTrigger.maxScroll(scroller || _win),\n\t_autoDistance = (el, progress) => { // for calculating the distance (and offset) for elements with speed: \"auto\". Progress is for if it's \"above the fold\" (negative start position), so we can crop as little as possible.\n\t\tlet parent = el.parentNode || _docEl,\n\t\t\tb1 = el.getBoundingClientRect(),\n\t\t\tb2 = parent.getBoundingClientRect(),\n\t\t\tgapTop = b2.top - b1.top,\n\t\t\tgapBottom = b2.bottom - b1.bottom,\n\t\t\tchange = (Math.abs(gapTop) > Math.abs(gapBottom) ? gapTop : gapBottom) / (1 - progress),\n\t\t\toffset = -change * progress,\n\t\t\tratio, extraChange;\n\t\tif (change > 0) { // if the image starts at the BOTTOM of the container, adjust things so that it shows as much of the image as possible while still covering.\n\t\t\tratio = b2.height / (_win.innerHeight + b2.height);\n\t\t\textraChange = ratio === 0.5 ? b2.height * 2 : Math.min(b2.height, Math.abs(-change * ratio / (2 * ratio - 1))) * 2 * (progress || 1);\n\t\t\toffset += progress ? -extraChange * progress : -extraChange / 2; // whatever the offset, we must double that in the opposite direction to compensate.\n\t\t\tchange += extraChange;\n\t\t}\n\t\treturn {change, offset};\n\t},\n\t_wrap = el => {\n\t\tlet wrapper = _doc.querySelector(\".ScrollSmoother-wrapper\"); // some frameworks load multiple times, so one already exists, just use that to avoid duplicates\n\t\tif (!wrapper) {\n\t\t\twrapper = _doc.createElement(\"div\");\n\t\t\twrapper.classList.add(\"ScrollSmoother-wrapper\");\n\t\t\tel.parentNode.insertBefore(wrapper, el);\n\t\t\twrapper.appendChild(el);\n\t\t}\n\t\treturn wrapper;\n\t};\n\nexport class ScrollSmoother {\n\n\tconstructor(vars) {\n\t\t_coreInitted || ScrollSmoother.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollSmoother)\");\n\t\tvars = this.vars = vars || {};\n\n\t\t_mainInstance && _mainInstance.kill();\n\t\t_mainInstance = this;\n\t\t_context(this);\n\n\t\tlet {smoothTouch, onUpdate, onStop, smooth, onFocusIn, normalizeScroll, wholePixels} = vars,\n\t\t\tcontent, wrapper, height, mainST, effects, sections, intervalID, wrapperCSS, contentCSS, paused, pausedNormalizer, recordedRefreshScroll, recordedRefreshScrub, allowUpdates,\n\t\t\tself = this,\n\t\t\teffectsPrefix = vars.effectsPrefix || \"\",\n\t\t\tscrollFunc = ScrollTrigger.getScrollFunc(_win),\n\t\t\tsmoothDuration = ScrollTrigger.isTouch === 1 ? (smoothTouch === true ? 0.8 : parseFloat(smoothTouch) || 0) : (smooth === 0 || smooth === false) ? 0 : parseFloat(smooth) || 0.8,\n\t\t\tspeed = (smoothDuration && +vars.speed) || 1,\n\t\t\tcurrentY = 0,\n\t\t\tdelta = 0,\n\t\t\tstartupPhase = 1,\n\t\t\ttracker = _getVelocityProp(0),\n\t\t\tupdateVelocity = () => tracker.update(-currentY),\n\t\t\tscroll = {y: 0},\n\t\t\tremoveScroll = () => content.style.overflow = \"visible\",\n\t\t\tisProxyScrolling,\n\t\t\tkillScrub = trigger => {\n\t\t\t\ttrigger.update(); // it's possible that it hasn't been synchronized with the actual scroll position yet, like if it's later in the _triggers Array. If it was already updated, it'll skip the processing anyway.\n\t\t\t\tlet scrub = trigger.getTween();\n\t\t\t\tif (scrub) {\n\t\t\t\t\tscrub.pause();\n\t\t\t\t\tscrub._time = scrub._dur; // force the playhead to completion without rendering just so that when it resumes, it doesn't jump back in the .resetTo().\n\t\t\t\t\tscrub._tTime = scrub._tDur;\n\t\t\t\t}\n\t\t\t\tisProxyScrolling = false;\n\t\t\t\ttrigger.animation.progress(trigger.progress, true);\n\t\t\t},\n\t\t\trender = (y, force) => {\n\t\t\t\tif ((y !== currentY && !paused) || force) {\n\t\t\t\t\twholePixels && (y = Math.round(y));\n\t\t\t\t\tif (smoothDuration) {\n\t\t\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n\t\t\t\t\t\t//content.style.transform = \"translateY(\" + y + \"px)\"; // NOTE: when we used matrix3d() or set will-change: transform, it performed noticeably worse on iOS counter-intuitively!\n\t\t\t\t\t\tcontent._gsap.y = y + \"px\";\n\t\t\t\t\t}\n\t\t\t\t\tdelta = y - currentY;\n\t\t\t\t\tcurrentY = y;\n\t\t\t\t\tScrollTrigger.isUpdating || ScrollSmoother.isRefreshing || ScrollTrigger.update(); // note: if we allowed an update() when in the middle of a refresh() it could render all the other ScrollTriggers and inside the update(), _refreshing would be true thus scrubs would jump instantly, but then on the very next update they'd continue from there. Basically this allowed update() to be called on OTHER ScrollTriggers during the refresh() of the mainST which could cause some complications. See https://gsap.com/forums/topic/35536-smoothscroller-ignoremobileresize-for-non-touch-devices\n\t\t\t\t}\n\t\t\t},\n\t\t\tscrollTop = function(value) {\n\t\t\t\tif (arguments.length) {\n\t\t\t\t\t(value < 0) && (value = 0);\n\t\t\t\t\tscroll.y = -value; // don't use currentY because we must accurately track the delta variable (in render() method)\n\t\t\t\t\tisProxyScrolling = true; // otherwise, if snapping was applied (or anything that attempted to SET the scroll proxy's scroll position), we'd set the scroll here which would then (on the next tick) update the content tween/ScrollTrigger which would try to smoothly animate to that new value, thus the scrub tween would impede the progress. So we use this flag to respond accordingly in the ScrollTrigger's onUpdate and effectively force the scrub to its end immediately.\n\t\t\t\t\tpaused ? (currentY = -value) : render(-value);\n\t\t\t\t\tScrollTrigger.isRefreshing ? mainST.update() : scrollFunc(value / speed); // during a refresh, we revert all scrollers to 0 and then put them back. We shouldn't force the window to that value too during the refresh.\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t\treturn -currentY;\n\t\t\t},\n\t\t\tresizeObserver = typeof(ResizeObserver) !== \"undefined\" && vars.autoResize !== false && new ResizeObserver(() => {\n\t\t\t\tif (!ScrollTrigger.isRefreshing) {\n\t\t\t\t\tlet max = _maxScroll(wrapper) * speed;\n\t\t\t\t\tmax < -currentY && scrollTop(max) // if the user scrolled down to the bottom, for example, and then the page resizes smaller, we should adjust things accordingly right away so that the scroll position isn't past the very end.\n\t\t\t\t\t_onResizeDelayedCall.restart(true);\n\t\t\t\t}\n\t\t\t}),\n\t\t\tlastFocusElement, // if the user clicks a button that scrolls the page, for example, then unfocuses the window and comes back and activates the window/tab again, it'll want to focus back on that same button element but in that case we should skip it. Only jump there when a new element gets focus, like tabbing for accessibility.\n\t\t\t_onFocusIn = e => { // when the focus changes, make sure that element is on-screen\n\t\t\t\twrapper.scrollTop = 0;\n\t\t\t\tif ((e.target.contains && e.target.contains(wrapper)) || (onFocusIn && onFocusIn(this, e) === false)) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tScrollTrigger.isInViewport(e.target) || (e.target === lastFocusElement) ||  this.scrollTo(e.target, false, \"center center\");\n\t\t\t\tlastFocusElement = e.target;\n\t\t\t},\n\t\t\t_transformPosition = (position, st) => { // feed in a position (start or end scroll value) and a ScrollTrigger that's associated with a parallax effect and it'll spit back the adjusted position based on the movement of the trigger. For example, if the trigger goes at a speed of 0.5 while in the viewport, we must push the start/end values of OTHER ScrollTriggers that use that same trigger further down to compensate.\n\t\t\t\tif (position < st.start) {\n\t\t\t\t\treturn position;\n\t\t\t\t}\n\t\t\t\tlet ratio = isNaN(st.ratio) ? 1 : st.ratio,\n\t\t\t\t\tchange = st.end - st.start,\n\t\t\t\t\tdistance = position - st.start,\n\t\t\t\t\toffset = st.offset || 0,\n\t\t\t\t\tpins = st.pins || [],\n\t\t\t\t\tpinOffset = pins.offset || 0,\n\t\t\t\t\tprogressOffset = (st._startClamp && st.start <= 0) || (st.pins && st.pins.offset) ? 0 : (st._endClamp && st.end === _maxScroll()) ? 1 : 0.5;\n\n\t\t\t\tpins.forEach(p => { // remove any pinning space/distance\n\t\t\t\t\tchange -= p.distance;\n\t\t\t\t\tif (p.nativeStart <= position) {\n\t\t\t\t\t\tdistance -= p.distance;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tif (pinOffset) { // edge case when a clamped effect starts mid-pin; we've gotta compensate for the smaller change amount (the yOffset gets set to the st.pins.offset, so let's say it clamps such that the page starts with the element pinned 100px in, we have to set the yOffset to 100 but then subtract 100 from the change value to compensate, thus we must scale the positions accordingly based on the ratios. Like if it would normally have a change of 2000, and a pin would normally hit at 1000, but we're offsetting by 100, that means everything must scale now that we're only moving 1900px rather than 2000px.\n\t\t\t\t\tdistance *= (change - pinOffset / ratio) / change;\n\t\t\t\t}\n\t\t\t\treturn position + (distance - offset * progressOffset) / ratio - distance;\n\t\t\t},\n\t\t\tadjustEffectRelatedTriggers = (st, triggers, partial) => { // if we're using this method to do only a partial Array of triggers, we should NOT reset or rebuild the pin data. For example, we tap into this from the offset() method.\n\t\t\t\tpartial || (st.pins.length = st.pins.offset = 0);\n\t\t\t\tlet pins = st.pins,\n\t\t\t\t\tmarkers = st.markers,\n\t\t\t\t\tdif, isClamped, start, end, nativeStart, nativeEnd, i, trig;\n\t\t\t\tfor (i = 0; i < triggers.length; i++) {\n\t\t\t\t\ttrig = triggers[i];\n\t\t\t\t\tif (st.trigger && trig.trigger && st !== trig && (trig.trigger === st.trigger || trig.pinnedContainer === st.trigger || st.trigger.contains(trig.trigger))) {\n\t\t\t\t\t\tnativeStart = trig._startNative || trig._startClamp || trig.start;\n\t\t\t\t\t\tnativeEnd = trig._endNative || trig._endClamp || trig.end;\n\t\t\t\t\t\tstart = _transformPosition(nativeStart, st);\n\t\t\t\t\t\t// note: _startClamp and _endClamp are populated with the unclamped values. For the sake of efficiency sake, we use the property both like a boolean to indicate that clamping is enabled AND the actual original unclamped value which we need in situations like if there's a data-speed=\"\" on an element that has something like start=\"clamp(top bottom)\". For in-viewport elements, it would clamp the values on the ScrollTrigger first, then feed it here and we'd adjust it on the clamped value which could throw things off - we need to apply the logic to the unclamped value and THEN re-apply clamping on the result.\n\t\t\t\t\t\tend = (trig.pin && nativeEnd > 0) ? start + (nativeEnd - nativeStart) : _transformPosition(nativeEnd, st);\n\t\t\t\t\t\ttrig.setPositions(start, end, true, (trig._startClamp ? Math.max(0, start) : start) - nativeStart); // the last value (pinOffset) is to adjust the pinStart y value inside ScrollTrigger to accommodate for the y offset that gets applied by the parallax effect.\n\t\t\t\t\t\ttrig.markerStart && markers.push(gsap.quickSetter([trig.markerStart, trig.markerEnd], \"y\", \"px\"));\n\t\t\t\t\t\tif (trig.pin && trig.end > 0 && !partial) {\n\t\t\t\t\t\t\tdif = trig.end - trig.start;\n\t\t\t\t\t\t\tisClamped = (st._startClamp && trig.start < 0);\n\t\t\t\t\t\t\tif (isClamped) {\n\t\t\t\t\t\t\t\tif (st.start > 0) { // the trigger element on the effect must have been pinned BEFORE its starting position, so in this edge case we must adjust the start position to be 0 and end position to get pushed further by the amount of the overlap\n\t\t\t\t\t\t\t\t\tst.setPositions(0, st.end + (st._startNative - st.start), true); // add the overlap amount\n\t\t\t\t\t\t\t\t\tadjustEffectRelatedTriggers(st, triggers);\n\t\t\t\t\t\t\t\t\treturn; // start over for this trigger element!\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tdif += trig.start;\n\t\t\t\t\t\t\t\tpins.offset = -trig.start; // edge case when a clamped effect starts mid-pin, we've gotta compensate in the onUpdate algorithm.\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpins.push({start: trig.start, nativeStart, end: trig.end, distance: dif, trig: trig});\n\t\t\t\t\t\t\tst.setPositions(st.start, st.end + (isClamped ? -trig.start : dif), true);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tadjustParallaxPosition = (triggers, createdAfterEffectWasApplied) => {\n\t\t\t\teffects.forEach(st => adjustEffectRelatedTriggers(st, triggers, createdAfterEffectWasApplied));\n\t\t\t},\n\t\t\tonRefresh = () => {\n\t\t\t\t_docEl = _doc.documentElement; // some frameworks like Astro may cache the <body> and replace it during routing, so we'll just re-record the _docEl and _body for safety (otherwise, the markers may not get added properly).\n\t\t\t\t_body = _doc.body;\n\t\t\t\tremoveScroll();\n\t\t\t\trequestAnimationFrame(removeScroll);\n\t\t\t\tif (effects) { // adjust all the effect start/end positions including any pins!\n\t\t\t\t\tScrollTrigger.getAll().forEach(st => { // record the native start/end positions because we'll be messing with them and need a way to have a \"source of truth\"\n\t\t\t\t\t\tst._startNative = st.start;\n\t\t\t\t\t\tst._endNative = st.end;\n\t\t\t\t\t});\n\t\t\t\t\teffects.forEach(st => {\n\t\t\t\t\t\tlet start = st._startClamp || st.start, // if it was already clamped, we should base things on the unclamped value and then do the clamping here.\n\t\t\t\t\t\t\tend = st.autoSpeed ? Math.min(_maxScroll(), st.end) : start + Math.abs((st.end - start) / st.ratio),\n\t\t\t\t\t\t\toffset = end - st.end; // we split the difference so that it reaches its natural position in the MIDDLE of the viewport\n\t\t\t\t\t\tstart -= offset / 2;\n\t\t\t\t\t\tend -= offset / 2;\n\t\t\t\t\t\tif (start > end) {\n\t\t\t\t\t\t\tlet s = start;\n\t\t\t\t\t\t\tstart = end;\n\t\t\t\t\t\t\tend = s;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (st._startClamp && start < 0) {\n\t\t\t\t\t\t\tend = st.ratio < 0 ? _maxScroll() : st.end / st.ratio;\n\t\t\t\t\t\t\toffset = end - st.end;\n\t\t\t\t\t\t\tstart = 0;\n\t\t\t\t\t\t} else if (st.ratio < 0 || (st._endClamp && end >= _maxScroll())) {\n\t\t\t\t\t\t\tend = _maxScroll();\n\t\t\t\t\t\t\tstart = st.ratio < 0 ? 0 : st.ratio > 1 ? 0 : end - (end - st.start) / st.ratio;\n\t\t\t\t\t\t\toffset = (end - start) * st.ratio - (st.end - st.start);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tst.offset = offset || 0.0001; // we assign at least a tiny value because we check in the onUpdate for .offset being set in order to apply values.\n\t\t\t\t\t\tst.pins.length = st.pins.offset = 0;\n\t\t\t\t\t\tst.setPositions(start, end, true);\n\t\t\t\t\t\t// note: another way of getting only the amount of offset traveled for a certain ratio is: distanceBetweenStartAndEnd * (1 / ratio - 1)\n\t\t\t\t\t});\n\t\t\t\t\tadjustParallaxPosition(ScrollTrigger.sort());\n\t\t\t\t}\n\t\t\t\ttracker.reset();\n\t\t\t},\n\t\t\taddOnRefresh = () => ScrollTrigger.addEventListener(\"refresh\", onRefresh),\n\t\t\trestoreEffects = () => effects && effects.forEach(st => st.vars.onRefresh(st)),\n\t\t\trevertEffects = () => {\n\t\t\t\teffects && effects.forEach(st => st.vars.onRefreshInit(st));\n\t\t\t\treturn restoreEffects;\n\t\t\t},\n\t\t\teffectValueGetter = (name, value, index, el) => {\n\t\t\t\treturn () => {\n\t\t\t\t\tlet v = typeof(value) === \"function\" ? value(index, el) : value;\n\t\t\t\t\tv || v === 0 || (v = el.getAttribute(\"data-\" + effectsPrefix + name) || (name === \"speed\" ? 1 : 0));\n\t\t\t\t\tel.setAttribute(\"data-\" + effectsPrefix + name, v);\n\t\t\t\t\tlet clamp = (v + \"\").substr(0, 6) === \"clamp(\";\n\t\t\t\t\treturn {clamp, value: clamp ? v.substr(6, v.length - 7) : v};\n\t\t\t\t};\n\t\t\t},\n\t\t\tcreateEffect = (el, speed, lag, index, effectsPadding) => {\n\t\t\t\teffectsPadding = (typeof(effectsPadding) === \"function\" ? effectsPadding(index, el) : effectsPadding) || 0;\n\t\t\t\tlet getSpeed = effectValueGetter(\"speed\", speed, index, el),\n\t\t\t\t\tgetLag = effectValueGetter(\"lag\", lag, index, el),\n\t\t\t\t\tstartY = gsap.getProperty(el, \"y\"),\n\t\t\t\t\tcache = el._gsap,\n\t\t\t\t\tratio, st, autoSpeed, scrub, progressOffset, yOffset,\n\t\t\t\t\tpins = [],\n\t\t\t\t\tinitDynamicValues = () => {\n\t\t\t\t\t\tspeed = getSpeed();\n\t\t\t\t\t\tlag = parseFloat(getLag().value);\n\t\t\t\t\t\tratio = parseFloat(speed.value) || 1;\n\t\t\t\t\t\tautoSpeed = speed.value === \"auto\";\n\t\t\t\t\t\tprogressOffset = autoSpeed || (st && st._startClamp && st.start <= 0) || pins.offset ? 0 : (st && st._endClamp && st.end === _maxScroll()) ? 1 : 0.5;\n\t\t\t\t\t\tscrub && scrub.kill();\n\t\t\t\t\t\tscrub = lag && gsap.to(el, {ease: _expo, overwrite: false, y: \"+=0\", duration: lag});\n\t\t\t\t\t\tif (st) {\n\t\t\t\t\t\t\tst.ratio = ratio;\n\t\t\t\t\t\t\tst.autoSpeed = autoSpeed;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\trevert = () => {\n\t\t\t\t\t\tcache.y = startY + \"px\";\n\t\t\t\t\t\tcache.renderTransform(1);\n\t\t\t\t\t\tinitDynamicValues();\n\t\t\t\t\t},\n\t\t\t\t\tmarkers = [],\n\t\t\t\t\tchange = 0,\n\t\t\t\t\tupdateChange = self => {\n\t\t\t\t\t\tif (autoSpeed) {\n\t\t\t\t\t\t\trevert();\n\t\t\t\t\t\t\tlet auto = _autoDistance(el, _clamp(0, 1, -self.start / (self.end - self.start)));\n\t\t\t\t\t\t\tchange = auto.change;\n\t\t\t\t\t\t\tyOffset = auto.offset;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tyOffset = pins.offset || 0;\n\t\t\t\t\t\t\tchange = (self.end - self.start - yOffset) * (1 - ratio);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tpins.forEach(p => change -= p.distance * (1 - ratio));\n\t\t\t\t\t\tself.offset = change || 0.001;\n\t\t\t\t\t\tself.vars.onUpdate(self);\n\t\t\t\t\t\tscrub && scrub.progress(1);\n\t\t\t\t\t};\n\t\t\t\tinitDynamicValues();\n\t\t\t\tif (ratio !== 1 || autoSpeed || scrub) {\n\t\t\t\t\tst = ScrollTrigger.create({\n\t\t\t\t\t\ttrigger: autoSpeed ? el.parentNode : el,\n\t\t\t\t\t\tstart: () => speed.clamp ? \"clamp(top bottom+=\" + effectsPadding + \")\" : \"top bottom+=\" + effectsPadding,\n\t\t\t\t\t\tend: () => speed.value < 0 ? \"max\" : speed.clamp ? \"clamp(bottom top-=\" + effectsPadding + \")\" : \"bottom top-=\" + effectsPadding,\n\t\t\t\t\t\tscroller: wrapper,\n\t\t\t\t\t\tscrub: true,\n\t\t\t\t\t\trefreshPriority: -999, // must update AFTER any other ScrollTrigger pins\n\t\t\t\t\t\tonRefreshInit: revert,\n\t\t\t\t\t\tonRefresh: updateChange,\n\t\t\t\t\t\tonKill: self => {\n\t\t\t\t\t\t\tlet i = effects.indexOf(self);\n\t\t\t\t\t\t\ti >= 0 && effects.splice(i, 1);\n\t\t\t\t\t\t\trevert();\n\t\t\t\t\t\t},\n\t\t\t\t\t\tonUpdate: self => {\n\t\t\t\t\t\t\tlet y = startY + change * (self.progress - progressOffset),\n\t\t\t\t\t\t\t\ti = pins.length,\n\t\t\t\t\t\t\t\textraY = 0,\n\t\t\t\t\t\t\t\tpin, scrollY, end;\n\t\t\t\t\t\t\tif (self.offset) { // wait until the effects are adjusted.\n\t\t\t\t\t\t\t\tif (i) { // pinning must be handled in a special way because when pinned, slope changes to 1.\n\t\t\t\t\t\t\t\t\tscrollY = -currentY; // -scroll.y;\n\t\t\t\t\t\t\t\t\tend = self.end;\n\t\t\t\t\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\t\t\t\t\tpin = pins[i];\n\t\t\t\t\t\t\t\t\t\tif (pin.trig.isActive || (scrollY >= pin.start && scrollY <= pin.end)) { // currently pinned so no need to set anything\n\t\t\t\t\t\t\t\t\t\t\tif (scrub) {\n\t\t\t\t\t\t\t\t\t\t\t\tpin.trig.progress += pin.trig.direction < 0 ? 0.001 : -0.001; // just to make absolutely sure that it renders (if the progress didn't change, it'll skip)\n\t\t\t\t\t\t\t\t\t\t\t\tpin.trig.update(0, 0, 1);\n\t\t\t\t\t\t\t\t\t\t\t\tscrub.resetTo(\"y\", parseFloat(cache.y), -delta, true);\n\t\t\t\t\t\t\t\t\t\t\t\tstartupPhase && scrub.progress(1);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t(scrollY > pin.end) && (extraY += pin.distance);\n\t\t\t\t\t\t\t\t\t\tend -= pin.distance;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\ty = startY + extraY + change * (((gsap.utils.clamp(self.start, self.end, scrollY) - self.start - extraY) / (end - self.start)) - progressOffset);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tmarkers.length && !autoSpeed && markers.forEach(setter => setter(y - extraY));\n\t\t\t\t\t\t\t\ty = _round(y + yOffset);\n\t\t\t\t\t\t\t\tif (scrub) {\n\t\t\t\t\t\t\t\t\tscrub.resetTo(\"y\", y, -delta, true);\n\t\t\t\t\t\t\t\t\tstartupPhase && scrub.progress(1);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tcache.y = y + \"px\";\n\t\t\t\t\t\t\t\t\tcache.renderTransform(1);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tupdateChange(st);\n\t\t\t\t\tgsap.core.getCache(st.trigger).stRevert = revertEffects; // if user calls ScrollSmoother.create() with effects and THEN creates a ScrollTrigger on the same trigger element, the effect would throw off the start/end positions thus we needed a way to revert things when creating a new ScrollTrigger in that scenario, so we use this stRevert property of the GSCache inside ScrollTrigger.\n\t\t\t\t\tst.startY = startY;\n\t\t\t\t\tst.pins = pins;\n\t\t\t\t\tst.markers = markers;\n\t\t\t\t\tst.ratio = ratio;\n\t\t\t\t\tst.autoSpeed = autoSpeed;\n\t\t\t\t\tel.style.willChange = \"transform\";\n\t\t\t\t}\n\t\t\t\treturn st;\n\t\t\t};\n\n\t\taddOnRefresh();\n\t\tScrollTrigger.addEventListener(\"killAll\", addOnRefresh);\n\t\tgsap.delayedCall(0.5, () => startupPhase = 0);\n\n\t\tthis.scrollTop = scrollTop;\n\n\t\tthis.scrollTo = (target, smooth, position) => {\n\t\t\tlet p = gsap.utils.clamp(0, _maxScroll(), isNaN(target) ? this.offset(target, position, !!smooth && !paused) : +target);\n\t\t\t!smooth ? scrollTop(p) : paused ? gsap.to(this, {duration: smoothDuration, scrollTop: p, overwrite: \"auto\", ease: _expo}) : scrollFunc(p);\n\t\t};\n\n\t\tthis.offset = (target, position, ignoreSpeed) => {\n\t\t\ttarget = _toArray(target)[0];\n\t\t\tlet cssText = target.style.cssText, // because if there's an effect applied, we revert(). We need to restore.\n\t\t\t\tst = ScrollTrigger.create({trigger: target, start: position || \"top top\"}),\n\t\t\t\ty;\n\t\t\tif (effects) {\n\t\t\t\tstartupPhase ? ScrollTrigger.refresh() : adjustParallaxPosition([st], true); // all the effects need to go through the initial full refresh() so that all the pins and ratios and offsets are set up. That's why we do a full refresh() if it's during the startupPhase.\n\t\t\t}\n\t\t\ty = st.start / (ignoreSpeed ? speed : 1);\n\t\t\tst.kill(false);\n\t\t\ttarget.style.cssText = cssText;\n\t\t\tgsap.core.getCache(target).uncache = 1;\n\t\t\treturn y;\n\t\t};\n\n\t\tfunction refreshHeight() {\n\t\t\theight = content.clientHeight;\n\t\t\tcontent.style.overflow = \"visible\"\n\t\t\t_body.style.height = (_win.innerHeight + (height - _win.innerHeight) / speed) + \"px\";\n\t\t\treturn (height - _win.innerHeight);\n\t\t}\n\n\t\tthis.content = function(element) {\n\t\t\tif (arguments.length) {\n\t\t\t\tlet newContent = _toArray(element || \"#smooth-content\")[0] || console.warn(\"ScrollSmoother needs a valid content element.\") || _body.children[0];\n\t\t\t\tif (newContent !== content) {\n\t\t\t\t\tcontent = newContent;\n\t\t\t\t\tcontentCSS = content.getAttribute(\"style\") || \"\";\n\t\t\t\t\tresizeObserver && resizeObserver.observe(content);\n\t\t\t\t\tgsap.set(content, {overflow: \"visible\", width: \"100%\", boxSizing: \"border-box\", y: \"+=0\"});\n\t\t\t\t\tsmoothDuration || gsap.set(content, {clearProps: \"transform\"});\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t}\n\t\t\treturn content;\n\t\t}\n\n\t\tthis.wrapper = function(element) {\n\t\t\tif (arguments.length) {\n\t\t\t\twrapper = _toArray(element || \"#smooth-wrapper\")[0] || _wrap(content);\n\t\t\t\twrapperCSS = wrapper.getAttribute(\"style\") || \"\";\n\t\t\t\trefreshHeight();\n\t\t\t\tgsap.set(wrapper, smoothDuration ? {overflow: \"hidden\", position: \"fixed\", height: \"100%\", width: \"100%\", top: 0, left: 0, right: 0, bottom: 0} : {overflow: \"visible\", position: \"relative\", width: \"100%\", height: \"auto\", top: \"auto\", bottom: \"auto\", left: \"auto\", right: \"auto\"});\n\t\t\t\treturn this;\n\t\t\t}\n\t\t\treturn wrapper;\n\t\t}\n\n\t\tthis.effects = (targets, config) => {\n\t\t\teffects || (effects = []);\n\t\t\tif (!targets) {\n\t\t\t\treturn effects.slice(0);\n\t\t\t}\n\t\t\ttargets = _toArray(targets);\n\t\t\ttargets.forEach(target => {\n\t\t\t\tlet i = effects.length;\n\t\t\t\twhile (i--) {\n\t\t\t\t\teffects[i].trigger === target && effects[i].kill(); // will automatically splice() it from the effects Array in the onKill\n\t\t\t\t}\n\t\t\t});\n\t\t\tconfig = config || {};\n\t\t\tlet {speed, lag, effectsPadding} = config,\n\t\t\t\teffectsToAdd = [],\n\t\t\t\ti, st;\n\t\t\tfor (i = 0; i < targets.length; i++) {\n\t\t\t\tst = createEffect(targets[i], speed, lag, i, effectsPadding);\n\t\t\t\tst && effectsToAdd.push(st);\n\t\t\t}\n\t\t\teffects.push(...effectsToAdd);\n\t\t\tconfig.refresh !== false && ScrollTrigger.refresh(); // certain effects require a refresh to work properly\n\t\t\treturn effectsToAdd;\n\t\t};\n\n\t\tthis.sections = (targets, config) => {\n\t\t\tsections || (sections = []);\n\t\t\tif (!targets) {\n\t\t\t\treturn sections.slice(0);\n\t\t\t}\n\t\t\tlet newSections = _toArray(targets).map(el => ScrollTrigger.create({\n\t\t\t\t\ttrigger: el,\n\t\t\t\t\tstart: \"top 120%\",\n\t\t\t\t\tend: \"bottom -20%\",\n\t\t\t\t\tonToggle: self => {\n\t\t\t\t\t\tel.style.opacity = self.isActive ? \"1\" : \"0\";\n\t\t\t\t\t\tel.style.pointerEvents = self.isActive ? \"all\" : \"none\";\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t);\n\t\t\tconfig && config.add ? sections.push(...newSections) : (sections = newSections.slice(0));\n\t\t\treturn newSections;\n\t\t}\n\n\t\tthis.content(vars.content);\n\t\tthis.wrapper(vars.wrapper);\n\t\tthis.render = y => render(y || y === 0 ? y : currentY);\n\t\tthis.getVelocity = () => tracker.getVelocity(-currentY);\n\n\t\tScrollTrigger.scrollerProxy(wrapper, {\n\t\t\tscrollTop: scrollTop,\n\t\t\tscrollHeight: () => refreshHeight() && _body.scrollHeight,\n\t\t\tfixedMarkers: vars.fixedMarkers !== false && !!smoothDuration,\n\t\t\tcontent: content,\n\t\t\tgetBoundingClientRect() {\n\t\t\t\treturn {top: 0, left: 0, width: _win.innerWidth, height: _win.innerHeight};\n\t\t\t}\n\t\t});\n\t\tScrollTrigger.defaults({scroller: wrapper});\n\t\tlet existingScrollTriggers = ScrollTrigger.getAll().filter(st => st.scroller === _win || st.scroller === wrapper);\n\t\texistingScrollTriggers.forEach(st => st.revert(true, true)); // in case it's in an environment like React where child components that have ScrollTriggers instantiate BEFORE the parent that does ScrollSmoother.create(...);\n\n\t\tmainST = ScrollTrigger.create({\n\t\t\tanimation: gsap.fromTo(scroll, {y: () => { allowUpdates = 0; return 0;}}, {\n\t\t\t\ty: () => {allowUpdates = 1; return -refreshHeight();},\n\t\t\t\timmediateRender: false,\n\t\t\t\tease: \"none\",\n\t\t\t\tdata: \"ScrollSmoother\",\n\t\t\t\tduration: 100, // for added precision\n\t\t\t\tonUpdate: function() {\n\t\t\t\t\tif (allowUpdates) { // skip when it's the \"from\" part of the tween (setting the startAt)\n\t\t\t\t\t\tlet force = isProxyScrolling;\n\t\t\t\t\t\tif (force) {\n\t\t\t\t\t\t\tkillScrub(mainST);\n\t\t\t\t\t\t\tscroll.y = currentY;\n\t\t\t\t\t\t}\n\t\t\t\t\t\trender(scroll.y, force);\n\t\t\t\t\t\tupdateVelocity();\n\t\t\t\t\t\tonUpdate && !paused && onUpdate(self);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}),\n\t\t\tonRefreshInit: self => {\n\t\t\t\tif (ScrollSmoother.isRefreshing) { // gets called on the onRefresh() when we do self.setPositions(...) in which case we should skip this\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tScrollSmoother.isRefreshing = true;\n\t\t\t\tif (effects) {\n\t\t\t\t\tlet pins = ScrollTrigger.getAll().filter(st => !!st.pin);\n\t\t\t\t\teffects.forEach(st => {\n\t\t\t\t\t\tif (!st.vars.pinnedContainer) {\n\t\t\t\t\t\t\tpins.forEach(pinST => {\n\t\t\t\t\t\t\t\tif (pinST.pin.contains(st.trigger)) {\n\t\t\t\t\t\t\t\t\tlet v = st.vars;\n\t\t\t\t\t\t\t\t\tv.pinnedContainer = pinST.pin;\n\t\t\t\t\t\t\t\t\tst.vars = null; // otherwise, it'll self.kill(), triggering the onKill()\n\t\t\t\t\t\t\t\t\tst.init(v, st.animation);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tlet scrub = self.getTween();\n\t\t\t\trecordedRefreshScrub = scrub && scrub._end > scrub._dp._time; // don't use scrub.progress() < 1 because we may have called killScrub() recently in which case it'll report progress() as 1 when we were actually in the middle of a scrub. That's why we tap into the _end instead.\n\t\t\t\trecordedRefreshScroll = currentY;\n\t\t\t\tscroll.y = 0;\n\t\t\t\tif (smoothDuration) {\n\t\t\t\t\tScrollTrigger.isTouch === 1 && (wrapper.style.position = \"absolute\"); // Safari 16 has a major bug - if you set wrapper.scrollTop to 0 (even if it's already 0), it blocks the whole page from scrolling page non-scrollable! See https://bugs.webkit.org/show_bug.cgi?id=245300 and https://codepen.io/GreenSock/pen/YzLZVOz. Originally we set pointer-events: none on the wrapper temporarily, and set it back to all after setting scrollTop to 0, but that could cause mouseenter/mouseleave/etc. events to fire too, so we opted to set the position to absolute and then back to fixed after setting scrollTop.\n\t\t\t\t\twrapper.scrollTop = 0; // set wrapper.scrollTop to 0 because in some very rare situations, the browser will auto-set that, like if there's a hash in the link or changing focus to an off-screen input\n\t\t\t\t\tScrollTrigger.isTouch === 1 && (wrapper.style.position = \"fixed\");\n\t\t\t\t}\n\t\t\t},\n\t\t\tonRefresh: self => {\n\t\t\t\tself.animation.invalidate(); // because pinnedContainers may have been found in ScrollTrigger's _refreshAll() that extend the height. Without this, it may prevent the user from being able to scroll all the way down.\n\t\t\t\tself.setPositions(self.start, refreshHeight() / speed);\n\t\t\t\trecordedRefreshScrub || killScrub(self);\n\t\t\t\tscroll.y = -scrollFunc() * speed; // in 3.11.1, we shifted to forcing the scroll position to 0 during the entire refreshAll() in ScrollTrigger and then restored the scroll position AFTER everything had been updated, thus we should always make these adjustments AFTER a full refresh rather than putting it in the onRefresh() of the individual mainST ScrollTrigger which would fire before the scroll position was restored.\n\t\t\t\trender(scroll.y);\n\t\t\t\tif (!startupPhase) {\n\t\t\t\t\trecordedRefreshScrub && (isProxyScrolling = false); // otherwise, we lose any in-progress scrub. When we set the progress(), it fires the onUpdate() which sets the scroll position immediately (jumps ahead if isProxyScrolling is true). See https://gsap.com/community/forums/topic/37515-dynamic-scrolltrigger-with-pin-inside-a-scrollsmoother/\n\t\t\t\t\tself.animation.progress(gsap.utils.clamp(0, 1, recordedRefreshScroll / speed / -self.end));\n\t\t\t\t}\n\t\t\t\tif (recordedRefreshScrub) { // we need to trigger the scrub to happen again\n\t\t\t\t\tself.progress -= 0.001;\n\t\t\t\t\tself.update();\n\t\t\t\t}\n\t\t\t\tScrollSmoother.isRefreshing = false;\n\t\t\t},\n\t\t\tid: \"ScrollSmoother\",\n\t\t\tscroller: _win,\n\t\t\tinvalidateOnRefresh: true,\n\t\t\tstart: 0,\n\t\t\trefreshPriority: -9999, // because all other pins, etc. should be calculated first before this figures out the height of the body. BUT this should also update FIRST so that the scroll position on the proxy is up-to-date when all the ScrollTriggers calculate their progress! -9999 is a special number that ScrollTrigger looks for to handle in this way.\n\t\t\tend: () => refreshHeight() / speed,\n\t\t\tonScrubComplete: () => {\n\t\t\t\ttracker.reset();\n\t\t\t\tonStop && onStop(this);\n\t\t\t},\n\t\t\tscrub: smoothDuration || true,\n\t\t});\n\n\t\tthis.smooth = function(value) {\n\t\t\tif (arguments.length) {\n\t\t\t\tsmoothDuration = value || 0;\n\t\t\t\tspeed = (smoothDuration && +vars.speed) || 1;\n\t\t\t\tmainST.scrubDuration(value);\n\t\t\t}\n\t\t\treturn mainST.getTween() ? mainST.getTween().duration() : 0;\n\t\t};\n\n\t\tmainST.getTween() && (mainST.getTween().vars.ease = vars.ease || _expo);\n\n\t\tthis.scrollTrigger = mainST;\n\n\t\tvars.effects && this.effects(vars.effects === true ? \"[data-\" + effectsPrefix + \"speed], [data-\" + effectsPrefix + \"lag]\" : vars.effects, {effectsPadding: vars.effectsPadding, refresh: false});\n\t\tvars.sections && this.sections(vars.sections === true ? \"[data-section]\" : vars.sections);\n\n\t\texistingScrollTriggers.forEach(st => {\n\t\t\tst.vars.scroller = wrapper;\n\t\t\tst.revert(false, true);\n\t\t\tst.init(st.vars, st.animation);\n\t\t});\n\n\t\tthis.paused = function(value, allowNestedScroll) {\n\t\t\tif (arguments.length) {\n\t\t\t\tif (!!paused !== value) {\n\t\t\t\t\tif (value) { // pause\n\t\t\t\t\t\tmainST.getTween() && mainST.getTween().pause();\n\t\t\t\t\t\tscrollFunc(-currentY / speed);\n\t\t\t\t\t\ttracker.reset();\n\t\t\t\t\t\tpausedNormalizer = ScrollTrigger.normalizeScroll();\n\t\t\t\t\t\tpausedNormalizer && pausedNormalizer.disable(); // otherwise the normalizer would try to scroll the page on things like wheel events.\n\t\t\t\t\t\tpaused = ScrollTrigger.observe({\n\t\t\t\t\t\t\tpreventDefault: true,\n\t\t\t\t\t\t\ttype: \"wheel,touch,scroll\",\n\t\t\t\t\t\t\tdebounce: false,\n\t\t\t\t\t\t\tallowClicks: true,\n\t\t\t\t\t\t\tonChangeY: () => scrollTop(-currentY) // refuse to scroll\n\t\t\t\t\t\t});\n\t\t\t\t\t\tpaused.nested = _inputObserver(_docEl, \"wheel,touch,scroll\", true, allowNestedScroll !== false); // allow nested scrolling, like modals\n\t\t\t\t\t} else { // resume\n\t\t\t\t\t\tpaused.nested.kill();\n\t\t\t\t\t\tpaused.kill();\n\t\t\t\t\t\tpaused = 0;\n\t\t\t\t\t\tpausedNormalizer && pausedNormalizer.enable();\n\t\t\t\t\t\tmainST.progress = (-currentY / speed - mainST.start) / (mainST.end - mainST.start);\n\t\t\t\t\t\tkillScrub(mainST);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t}\n\t\t\treturn !!paused;\n\t\t};\n\n\t\tthis.kill = this.revert = () => {\n\t\t\tthis.paused(false);\n\t\t\tkillScrub(mainST);\n\t\t\tmainST.kill();\n\t\t\tlet triggers = (effects || []).concat(sections || []),\n\t\t\t\ti = triggers.length;\n\t\t\twhile (i--) { // make sure we go backwards because the onKill() will effects.splice(index, 1) and we don't want to skip\n\t\t\t\ttriggers[i].kill();\n\t\t\t}\n\t\t\tScrollTrigger.scrollerProxy(wrapper);\n\t\t\tScrollTrigger.removeEventListener(\"killAll\", addOnRefresh);\n\t\t\tScrollTrigger.removeEventListener(\"refresh\", onRefresh);\n\t\t\twrapper.style.cssText = wrapperCSS;\n\t\t\tcontent.style.cssText = contentCSS;\n\t\t\tlet defaults = ScrollTrigger.defaults({});\n\t\t\tdefaults && defaults.scroller === wrapper && ScrollTrigger.defaults({scroller: _win});\n\t\t\tthis.normalizer && ScrollTrigger.normalizeScroll(false);\n\t\t\tclearInterval(intervalID);\n\t\t\t_mainInstance = null;\n\t\t\tresizeObserver && resizeObserver.disconnect();\n\t\t\t_body.style.removeProperty(\"height\");\n\t\t\t_win.removeEventListener(\"focusin\", _onFocusIn);\n\t\t}\n\n\t\tthis.refresh = (soft, force) => mainST.refresh(soft, force);\n\n\t\tif (normalizeScroll) {\n\t\t\tthis.normalizer = ScrollTrigger.normalizeScroll(normalizeScroll === true ? { debounce: true, content: !smoothDuration && content } : normalizeScroll);\n\t\t}\n\n\t\tScrollTrigger.config(vars); // in case user passes in ignoreMobileResize for example\n\t\t// (\"overscrollBehavior\" in _win.getComputedStyle(_body)) && gsap.set([_body, _docEl], {overscrollBehavior: \"none\"}); // this caused Safari 17+ not to scroll the entire page (bug in Safari), so let people set this in the CSS instead if they want.\n\t\t(\"scrollBehavior\" in _win.getComputedStyle(_body)) && gsap.set([_body, _docEl], {scrollBehavior: \"auto\"});\n\n\t\t// if the user hits the tab key (or whatever) to shift focus to an element that's off-screen, center that element.\n\t\t_win.addEventListener(\"focusin\", _onFocusIn);\n\n\t\tintervalID = setInterval(updateVelocity, 250);\n\n\t\t_doc.readyState === \"loading\" || requestAnimationFrame(() => ScrollTrigger.refresh());\n\n\t}\n\n\tget progress() {\n\t\treturn this.scrollTrigger ? this.scrollTrigger.animation._time / 100 : 0;\n\t}\n\n\n\tstatic register(core) {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = core || _getGSAP();\n\t\t\tif (_windowExists() && window.document) {\n\t\t\t\t_win = window;\n\t\t\t\t_doc = document;\n\t\t\t\t_docEl = _doc.documentElement;\n\t\t\t\t_body = _doc.body;\n\t\t\t}\n\t\t\tif (gsap) {\n\t\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t\t_expo = gsap.parseEase(\"expo\");\n\t\t\t\t_context = gsap.core.context || function() {};\n\t\t\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\t\t\tgsap.core.globals(\"ScrollSmoother\", ScrollSmoother); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\t\t\t\tif (_body && ScrollTrigger) {\n\t\t\t\t\t_onResizeDelayedCall = gsap.delayedCall(0.2, () => ScrollTrigger.isRefreshing || (_mainInstance && _mainInstance.refresh())).pause();\n\t\t\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t\t\t_getVelocityProp = ScrollTrigger.core._getVelocityProp;\n\t\t\t\t\t_inputObserver = ScrollTrigger.core._inputObserver;\n\t\t\t\t\tScrollSmoother.refresh = ScrollTrigger.refresh;\n\t\t\t\t\t_coreInitted = 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn _coreInitted;\n\t}\n\n\n}\n\nScrollSmoother.version = \"3.13.0\";\nScrollSmoother.create = vars => (_mainInstance && vars && _mainInstance.content() === _toArray(vars.content)[0]) ? _mainInstance : new ScrollSmoother(vars);\nScrollSmoother.get = () => _mainInstance;\n\n_getGSAP() && gsap.registerPlugin(ScrollSmoother);\n\nexport { ScrollSmoother as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_maxScroll", "scroller", "ScrollTrigger", "maxScroll", "_win", "_coreInitted", "_doc", "_docEl", "_body", "_toArray", "_clamp", "_mainInstance", "_expo", "_getVelocityProp", "_inputObserver", "_context", "_onResizeDelayedCall", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "core", "document", "documentElement", "body", "utils", "toArray", "clamp", "parseEase", "context", "globals", "delayedCall", "isRefreshing", "refresh", "pause", "this", "scrollTrigger", "animation", "_time", "vars", "console", "warn", "kill", "updateVelocity", "tracker", "update", "currentY", "removeScroll", "content", "style", "overflow", "killScrub", "trigger", "scrub", "getTween", "_dur", "_tTime", "_tDur", "isProxyScrolling", "progress", "render", "y", "force", "paused", "wholePixels", "Math", "round", "smoothDuration", "transform", "_gsap", "delta", "isUpdating", "scrollTop", "value", "arguments", "length", "scroll", "mainST", "scrollFunc", "speed", "_onFocusIn", "e", "wrapper", "target", "contains", "onFocusIn", "_this", "isInViewport", "lastFocusElement", "scrollTo", "_transformPosition", "position", "st", "start", "ratio", "isNaN", "change", "end", "distance", "offset", "pins", "pinOffset", "progressOffset", "_startClamp", "_endClamp", "for<PERSON>ach", "p", "nativeStart", "adjustParallaxPosition", "triggers", "createdAfterEffectWasApplied", "effects", "adjustEffectRelatedTriggers", "partial", "dif", "isClamped", "nativeEnd", "i", "trig", "markers", "<PERSON><PERSON><PERSON><PERSON>", "_startNative", "_endNative", "pin", "setPositions", "max", "markerStart", "push", "quickSetter", "markerEnd", "onRefresh", "requestAnimationFrame", "getAll", "autoSpeed", "min", "abs", "s", "sort", "reset", "addOnRefresh", "addEventListener", "restoreEffects", "revertEffects", "onRefreshInit", "effectValueGetter", "name", "index", "el", "v", "getAttribute", "effectsPrefix", "setAttribute", "substr", "createEffect", "lag", "effectsPadding", "initDynamicValues", "getSpeed", "parseFloat", "getLag", "to", "ease", "overwrite", "duration", "revert", "cache", "startY", "renderTransform", "updateChange", "self", "auto", "_autoDistance", "extraChange", "parent", "parentNode", "b1", "getBoundingClientRect", "b2", "gapTop", "top", "gapBottom", "bottom", "height", "innerHeight", "yOffset", "onUpdate", "getProperty", "create", "refreshPriority", "onKill", "indexOf", "splice", "scrollY", "extraY", "isActive", "direction", "resetTo", "startupPhase", "setter", "_round", "getCache", "stRevert", "<PERSON><PERSON><PERSON><PERSON>", "sections", "intervalID", "wrapperCSS", "contentCSS", "pausedNormalizer", "recordedRefreshScroll", "recordedRefreshScrub", "allowUpdates", "smoothTouch", "onStop", "smooth", "normalizeScroll", "getScrollFunc", "is<PERSON><PERSON>ch", "resizeObserver", "ResizeObserver", "autoResize", "restart", "refreshHeight", "clientHeight", "ignoreSpeed", "cssText", "uncache", "element", "newContent", "children", "observe", "set", "width", "boxSizing", "clearProps", "_wrap", "querySelector", "createElement", "classList", "add", "insertBefore", "append<PERSON><PERSON><PERSON>", "left", "right", "targets", "config", "slice", "effectsToAdd", "newSections", "map", "onToggle", "opacity", "pointerEvents", "getVelocity", "scrollerProxy", "scrollHeight", "fixedMarkers", "innerWidth", "defaults", "existingScrollTriggers", "filter", "fromTo", "immediateRender", "data", "pinST", "init", "_end", "_dp", "invalidate", "id", "invalidateOnRefresh", "onScrubComplete", "scrubDuration", "allowNestedScroll", "disable", "preventDefault", "type", "debounce", "allowClicks", "onChangeY", "nested", "enable", "concat", "removeEventListener", "normalizer", "clearInterval", "disconnect", "removeProperty", "soft", "getComputedStyle", "scroll<PERSON>eh<PERSON>or", "setInterval", "readyState", "version", "get"], "mappings": ";;;;;;;;;mYAWiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAG/E,SAAbE,EAAaC,UAAYC,EAAcC,UAAUF,GAAYG,OAL1DN,EAAMO,EAAcD,EAAME,EAAMC,EAAQC,EAAcC,EAAUC,EAAQR,EAAeS,EAAeC,EAAOC,EAAkBC,EAAgBC,EAAUC,EAkChJC,kBAslBLC,SAAP,kBAAgBC,UACVd,IACJP,EAAOqB,GAAQtB,IACXF,KAAmBC,OAAOwB,WAC7BhB,EAAOR,OACPU,EAAOc,SACPb,EAASD,EAAKe,gBACdb,EAAQF,EAAKgB,MAEVxB,IACHW,EAAWX,EAAKyB,MAAMC,QACtBd,EAASZ,EAAKyB,MAAME,MACpBb,EAAQd,EAAK4B,UAAU,QACvBX,EAAWjB,EAAKqB,KAAKQ,SAAW,aAChCzB,EAAgBJ,EAAKqB,KAAKS,UAAU1B,cACpCJ,EAAKqB,KAAKS,QAAQ,iBAAkBX,gBAChCT,GAASN,IACZc,EAAuBlB,EAAK+B,YAAY,GAAK,kBAAM3B,EAAc4B,cAAiBnB,GAAiBA,EAAcoB,YAAYC,QAE7HnB,EAAmBX,EAAciB,KAAKN,iBACtCC,EAAiBZ,EAAciB,KAAKL,eACpCG,eAAec,QAAU7B,EAAc6B,QACvC1B,EAAe,KAIXA,kKA9BA4B,KAAKC,cAAgBD,KAAKC,cAAcC,UAAUC,MAAQ,IAAM,8CAhlB5DC,cACXhC,GAAgBY,eAAeC,SAASpB,IAASwC,QAAQC,KAAK,8CAC9DF,EAAOJ,KAAKI,KAAOA,GAAQ,GAE3B1B,GAAiBA,EAAc6B,OAE/BzB,EADAJ,EAAgBsB,MAcE,SAAjBQ,YAAuBC,EAAQC,QAAQC,GAExB,SAAfC,YAAqBC,EAAQC,MAAMC,SAAW,UAElC,SAAZC,GAAYC,GACXA,EAAQP,aACJQ,EAAQD,EAAQE,WAChBD,IACHA,EAAMnB,QACNmB,EAAMf,MAAQe,EAAME,KACpBF,EAAMG,OAASH,EAAMI,OAEtBC,GAAmB,EACnBN,EAAQf,UAAUsB,SAASP,EAAQO,UAAU,GAErC,SAATC,GAAUC,EAAGC,IACPD,IAAMf,IAAaiB,GAAWD,KAClCE,IAAgBH,EAAII,KAAKC,MAAML,IAC3BM,IACHnB,EAAQC,MAAMmB,UAAY,mDAAqDP,EAAI,UAEnFb,EAAQqB,MAAMR,EAAIA,EAAI,MAEvBS,EAAQT,EAAIf,EACZA,EAAWe,EACXzD,EAAcmE,YAAcpD,eAAea,cAAgB5B,EAAcyC,UAG/D,SAAZ2B,GAAqBC,UAChBC,UAAUC,QACZF,EAAQ,IAAOA,EAAQ,GACxBG,EAAOf,GAAKY,EACZf,GAAmB,EACnBK,EAAUjB,GAAY2B,EAASb,IAAQa,GACvCrE,EAAc4B,aAAe6C,EAAOhC,SAAWiC,EAAWL,EAAQM,GAC3D5C,OAEAW,EAUI,SAAbkC,GAAaC,GACZC,EAAQV,UAAY,EACfS,EAAEE,OAAOC,UAAYH,EAAEE,OAAOC,SAASF,IAAcG,IAAoC,IAAvBA,EAAUC,EAAML,KAGvF7E,EAAcmF,aAAaN,EAAEE,SAAYF,EAAEE,SAAWK,GAAsBF,EAAKG,SAASR,EAAEE,QAAQ,EAAO,iBAC3GK,EAAmBP,EAAEE,QAED,SAArBO,GAAsBC,EAAUC,MAC3BD,EAAWC,EAAGC,aACVF,MAEJG,EAAQC,MAAMH,EAAGE,OAAS,EAAIF,EAAGE,MACpCE,EAASJ,EAAGK,IAAML,EAAGC,MACrBK,EAAWP,EAAWC,EAAGC,MACzBM,EAASP,EAAGO,QAAU,EACtBC,EAAOR,EAAGQ,MAAQ,GAClBC,EAAYD,EAAKD,QAAU,EAC3BG,EAAkBV,EAAGW,aAAeX,EAAGC,OAAS,GAAOD,EAAGQ,MAAQR,EAAGQ,KAAKD,OAAU,EAAKP,EAAGY,WAAaZ,EAAGK,MAAQ/F,IAAgB,EAAI,UAEzIkG,EAAKK,QAAQ,SAAAC,GACZV,GAAUU,EAAER,SACRQ,EAAEC,aAAehB,IACpBO,GAAYQ,EAAER,YAGZG,IACHH,IAAaF,EAASK,EAAYP,GAASE,GAErCL,GAAYO,EAAWC,EAASG,GAAkBR,EAAQI,EAmCzC,SAAzBU,GAA0BC,EAAUC,GACnCC,EAAQN,QAAQ,SAAAb,UAlCa,SAA9BoB,4BAA+BpB,EAAIiB,EAAUI,GAC5CA,IAAYrB,EAAGQ,KAAKzB,OAASiB,EAAGQ,KAAKD,OAAS,OAG7Ce,EAAKC,EAAWtB,EAAOI,EAAKU,EAAaS,EAAWC,EAAGC,EAFpDlB,EAAOR,EAAGQ,KACbmB,EAAU3B,EAAG2B,YAETF,EAAI,EAAGA,EAAIR,EAASlC,OAAQ0C,OAChCC,EAAOT,EAASQ,GACZzB,EAAGxC,SAAWkE,EAAKlE,SAAWwC,IAAO0B,IAASA,EAAKlE,UAAYwC,EAAGxC,SAAWkE,EAAKE,kBAAoB5B,EAAGxC,SAAWwC,EAAGxC,QAAQgC,SAASkC,EAAKlE,YAChJuD,EAAcW,EAAKG,cAAgBH,EAAKf,aAAee,EAAKzB,MAC5DuB,EAAYE,EAAKI,YAAcJ,EAAKd,WAAac,EAAKrB,IACtDJ,EAAQH,GAAmBiB,EAAaf,GAExCK,EAAOqB,EAAKK,KAAmB,EAAZP,EAAiBvB,GAASuB,EAAYT,GAAejB,GAAmB0B,EAAWxB,GACtG0B,EAAKM,aAAa/B,EAAOI,GAAK,GAAOqB,EAAKf,YAActC,KAAK4D,IAAI,EAAGhC,GAASA,GAASc,GACtFW,EAAKQ,aAAeP,EAAQQ,KAAK/H,EAAKgI,YAAY,CAACV,EAAKQ,YAAaR,EAAKW,WAAY,IAAK,OACvFX,EAAKK,KAAkB,EAAXL,EAAKrB,MAAYgB,GAAS,IACzCC,EAAMI,EAAKrB,IAAMqB,EAAKzB,MACtBsB,EAAavB,EAAGW,aAAee,EAAKzB,MAAQ,EAC7B,IACC,EAAXD,EAAGC,aACND,EAAGgC,aAAa,EAAGhC,EAAGK,KAAOL,EAAG6B,aAAe7B,EAAGC,QAAQ,QAC1DmB,4BAA4BpB,EAAIiB,GAGjCK,GAAOI,EAAKzB,MACZO,EAAKD,QAAUmB,EAAKzB,MAErBO,EAAK2B,KAAK,CAAClC,MAAOyB,EAAKzB,MAAOc,YAAAA,EAAaV,IAAKqB,EAAKrB,IAAKC,SAAUgB,EAAKI,KAAMA,IAC/E1B,EAAGgC,aAAahC,EAAGC,MAAOD,EAAGK,KAAOkB,GAAaG,EAAKzB,MAAQqB,IAAM,IAMjDF,CAA4BpB,EAAIiB,EAAUC,KAErD,SAAZoB,KACCzH,EAASD,EAAKe,gBACdb,EAAQF,EAAKgB,KACbuB,KACAoF,sBAAsBpF,IAClBgE,IACH3G,EAAcgI,SAAS3B,QAAQ,SAAAb,GAC9BA,EAAG6B,aAAe7B,EAAGC,MACrBD,EAAG8B,WAAa9B,EAAGK,MAEpBc,EAAQN,QAAQ,SAAAb,OACXC,EAAQD,EAAGW,aAAeX,EAAGC,MAChCI,EAAML,EAAGyC,UAAYpE,KAAKqE,IAAIpI,IAAc0F,EAAGK,KAAOJ,EAAQ5B,KAAKsE,KAAK3C,EAAGK,IAAMJ,GAASD,EAAGE,OAC7FK,EAASF,EAAML,EAAGK,QAEnBA,GAAOE,EAAS,IADhBN,GAASM,EAAS,GAED,KACZqC,EAAI3C,EACRA,EAAQI,EACRA,EAAMuC,EAEH5C,EAAGW,aAAeV,EAAQ,GAE7BM,GADAF,EAAML,EAAGE,MAAQ,EAAI5F,IAAe0F,EAAGK,IAAML,EAAGE,OACjCF,EAAGK,IAClBJ,EAAQ,IACED,EAAGE,MAAQ,GAAMF,EAAGY,WAAaP,GAAO/F,OAGlDiG,IAFAF,EAAM/F,MACN2F,EAAQD,EAAGE,MAAQ,GAAmB,EAAXF,EAAGE,MAAP,EAAuBG,GAAOA,EAAML,EAAGC,OAASD,EAAGE,QACjDF,EAAGE,OAASF,EAAGK,IAAML,EAAGC,QAElDD,EAAGO,OAASA,GAAU,KACtBP,EAAGQ,KAAKzB,OAASiB,EAAGQ,KAAKD,OAAS,EAClCP,EAAGgC,aAAa/B,EAAOI,GAAK,KAG7BW,GAAuBxG,EAAcqI,SAEtC7F,EAAQ8F,QAEM,SAAfC,YAAqBvI,EAAcwI,iBAAiB,UAAWV,IAC9C,SAAjBW,YAAuB9B,GAAWA,EAAQN,QAAQ,SAAAb,UAAMA,EAAGrD,KAAK2F,UAAUtC,KAC1D,SAAhBkD,YACC/B,GAAWA,EAAQN,QAAQ,SAAAb,UAAMA,EAAGrD,KAAKwG,cAAcnD,KAChDiD,GAEY,SAApBG,GAAqBC,EAAMxE,EAAOyE,EAAOC,UACjC,eACFC,EAAsB,mBAAX3E,EAAwBA,EAAMyE,EAAOC,GAAM1E,EAC1D2E,GAAW,IAANA,IAAYA,EAAID,EAAGE,aAAa,QAAUC,EAAgBL,KAAmB,UAATA,EAAmB,EAAI,IAChGE,EAAGI,aAAa,QAAUD,EAAgBL,EAAMG,OAC5CzH,EAAkC,YAAzByH,EAAI,IAAII,OAAO,EAAG,SACxB,CAAC7H,MAAAA,EAAO8C,MAAO9C,EAAQyH,EAAEI,OAAO,EAAGJ,EAAEzE,OAAS,GAAKyE,IAG7C,SAAfK,GAAgBN,EAAIpE,EAAO2E,EAAKR,EAAOS,GAQjB,SAApBC,KACC7E,EAAQ8E,IACRH,EAAMI,WAAWC,IAAStF,OAC1BqB,EAAQgE,WAAW/E,EAAMN,QAAU,EACnC4D,EAA4B,SAAhBtD,EAAMN,MAClB6B,EAAiB+B,GAAczC,GAAMA,EAAGW,aAAeX,EAAGC,OAAS,GAAMO,EAAKD,OAAS,EAAKP,GAAMA,EAAGY,WAAaZ,EAAGK,MAAQ/F,IAAgB,EAAI,GACjJmD,GAASA,EAAMX,OACfW,EAAQqG,GAAO1J,EAAKgK,GAAGb,EAAI,CAACc,KAAMnJ,EAAOoJ,WAAW,EAAOrG,EAAG,MAAOsG,SAAUT,IAC3E9D,IACHA,EAAGE,MAAQA,EACXF,EAAGyC,UAAYA,GAGR,SAAT+B,KACCC,EAAMxG,EAAIyG,EAAS,KACnBD,EAAME,gBAAgB,GACtBX,KAIc,SAAfY,GAAeC,MACVpC,EAAW,CACd+B,SACIM,EAxPM,SAAhBC,cAAiBxB,EAAIxF,OAQnBmC,EAAO8E,EAPJC,EAAS1B,EAAG2B,YAAcrK,EAC7BsK,EAAK5B,EAAG6B,wBACRC,EAAKJ,EAAOG,wBACZE,EAASD,EAAGE,IAAMJ,EAAGI,IACrBC,EAAYH,EAAGI,OAASN,EAAGM,OAC3BrF,GAAU/B,KAAKsE,IAAI2C,GAAUjH,KAAKsE,IAAI6C,GAAaF,EAASE,IAAc,EAAIzH,GAC9EwC,GAAUH,EAASrC,SAEP,EAATqC,IAEH4E,EAAwB,KADxB9E,EAAQmF,EAAGK,QAAUhL,EAAKiL,YAAcN,EAAGK,SACD,EAAZL,EAAGK,OAAgF,EAAnErH,KAAKqE,IAAI2C,EAAGK,OAAQrH,KAAKsE,KAAKvC,EAASF,GAAS,EAAIA,EAAQ,MAAYnC,GAAY,GAClIwC,GAAUxC,GAAYiH,EAAcjH,GAAYiH,EAAc,EAC9D5E,GAAU4E,GAEJ,CAAC5E,OAAAA,EAAQG,OAAAA,GAyOAwE,CAAcxB,EAAIvI,EAAO,EAAG,GAAI6J,EAAK5E,OAAS4E,EAAKxE,IAAMwE,EAAK5E,SACzEG,EAAS0E,EAAK1E,OACdwF,EAAUd,EAAKvE,YAEfqF,EAAUpF,EAAKD,QAAU,EACzBH,GAAUyE,EAAKxE,IAAMwE,EAAK5E,MAAQ2F,IAAY,EAAI1F,GAEnDM,EAAKK,QAAQ,SAAAC,UAAKV,GAAUU,EAAER,UAAY,EAAIJ,KAC9C2E,EAAKtE,OAASH,GAAU,KACxByE,EAAKlI,KAAKkJ,SAAShB,GACnBpH,GAASA,EAAMM,SAAS,GAxC1BgG,GAA6C,mBAApBA,EAAiCA,EAAeT,EAAOC,GAAMQ,IAAmB,MAKxG7D,EAAOF,EAAIyC,EAAWhF,EAAOiD,EAAgBkF,EAJ1C3B,EAAWb,GAAkB,QAASjE,EAAOmE,EAAOC,GACvDY,EAASf,GAAkB,MAAOU,EAAKR,EAAOC,GAC9CmB,EAAStK,EAAK0L,YAAYvC,EAAI,KAC9BkB,EAAQlB,EAAG9E,MAEX+B,EAAO,GAmBPmB,EAAU,GACVvB,EAAS,SAgBV4D,MACc,IAAV9D,GAAeuC,GAAahF,KAoD/BmH,GAnDA5E,EAAKxF,EAAcuL,OAAO,CACzBvI,QAASiF,EAAYc,EAAG2B,WAAa3B,EACrCtD,MAAO,wBAAMd,EAAMpD,MAAQ,qBAAuBgI,EAAiB,IAAM,eAAiBA,GAC1F1D,IAAK,sBAAMlB,EAAMN,MAAQ,EAAI,MAAQM,EAAMpD,MAAQ,qBAAuBgI,EAAiB,IAAM,eAAiBA,GAClHxJ,SAAU+E,EACV7B,OAAO,EACPuI,iBAAkB,IAClB7C,cAAeqB,GACflC,UAAWsC,GACXqB,OAAQ,gBAAApB,OACHpD,EAAIN,EAAQ+E,QAAQrB,GACnB,GAALpD,GAAUN,EAAQgF,OAAO1E,EAAG,GAC5B+C,MAEDqB,SAAU,kBAAAhB,OAIR9C,EAAKqE,EAAS/F,EAHXpC,EAAIyG,EAAStE,GAAUyE,EAAK9G,SAAW2C,GAC1Ce,EAAIjB,EAAKzB,OACTsH,EAAS,KAENxB,EAAKtE,OAAQ,IACZkB,EAAG,KACN2E,GAAWlJ,EACXmD,EAAMwE,EAAKxE,IACJoB,KAAK,KACXM,EAAMvB,EAAKiB,IACHC,KAAK4E,UAAaF,GAAWrE,EAAI9B,OAASmG,GAAWrE,EAAI1B,gBAC5D5C,IACHsE,EAAIL,KAAK3D,UAAYgE,EAAIL,KAAK6E,UAAY,EAAI,MAAS,KACvDxE,EAAIL,KAAKzE,OAAO,EAAG,EAAG,GACtBQ,EAAM+I,QAAQ,IAAKtC,WAAWO,EAAMxG,IAAKS,GAAO,GAChD+H,GAAgBhJ,EAAMM,SAAS,KAIhCqI,EAAUrE,EAAI1B,MAASgG,GAAUtE,EAAIzB,UACtCD,GAAO0B,EAAIzB,SAEZrC,EAAIyG,EAAS2B,EAASjG,IAAYhG,EAAKyB,MAAME,MAAM8I,EAAK5E,MAAO4E,EAAKxE,IAAK+F,GAAWvB,EAAK5E,MAAQoG,IAAWhG,EAAMwE,EAAK5E,OAAUS,GAElIiB,EAAQ5C,SAAW0D,GAAad,EAAQd,QAAQ,SAAA6F,UAAUA,EAAOzI,EAAIoI,KACrEpI,EAhTE,SAAT0I,OAAS9H,UAASR,KAAKC,MAAc,IAARO,GAAkB,KAAU,EAgT9C8H,CAAO1I,EAAI2H,GACXnI,GACHA,EAAM+I,QAAQ,IAAKvI,GAAIS,GAAO,GAC9B+H,GAAgBhJ,EAAMM,SAAS,KAE/B0G,EAAMxG,EAAIA,EAAI,KACdwG,EAAME,gBAAgB,SAM1BvK,EAAKqB,KAAKmL,SAAS5G,EAAGxC,SAASqJ,SAAW3D,GAC1ClD,EAAG0E,OAASA,EACZ1E,EAAGQ,KAAOA,EACVR,EAAG2B,QAAUA,EACb3B,EAAGE,MAAQA,EACXF,EAAGyC,UAAYA,EACfc,EAAGlG,MAAMyJ,WAAa,aAEhB9G,MA3RR5C,EAASkC,EAASoG,EAAQzG,EAAQkC,EAAS4F,EAAUC,EAAYC,EAAYC,EAAY/I,EAAQgJ,EAAkBC,EAAuBC,EAAsBC,EAahKxJ,EA2CA8B,EAzDI2H,EAAkF5K,EAAlF4K,YAAa1B,EAAqElJ,EAArEkJ,SAAU2B,EAA2D7K,EAA3D6K,OAAQC,EAAmD9K,EAAnD8K,OAAQhI,EAA2C9C,EAA3C8C,UAAWiI,EAAgC/K,EAAhC+K,gBAAiBtJ,EAAezB,EAAfyB,YAEvEyG,EAAOtI,KACPmH,EAAgB/G,EAAK+G,eAAiB,GACtCxE,EAAa1E,EAAcmN,cAAcjN,GACzC6D,EAA2C,IAA1B/D,EAAcoN,SAAiC,IAAhBL,EAAuB,GAAMrD,WAAWqD,IAAgB,EAAiB,IAAXE,IAA2B,IAAXA,EAAoB,EAAIvD,WAAWuD,IAAW,GAC5KtI,EAASZ,IAAmB5B,EAAKwC,OAAU,EAC3CjC,EAAW,EACXwB,EAAQ,EACR+H,EAAe,EACfzJ,EAAU7B,EAAiB,GAE3B6D,EAAS,CAACf,EAAG,GAsCb4J,EAA4C,oBAApBC,iBAAuD,IAApBnL,EAAKoL,YAAwB,IAAID,eAAe,eACrGtN,EAAc4B,aAAc,KAC5B6F,EAAM3H,EAAWgF,GAAWH,EAChC8C,GAAO/E,GAAY0B,GAAUqD,GAC7B3G,EAAqB0M,SAAQ,eAmQvBC,uBACRvC,EAAStI,EAAQ8K,aACjB9K,EAAQC,MAAMC,SAAW,UACzBxC,EAAMuC,MAAMqI,OAAUhL,EAAKiL,aAAeD,EAAShL,EAAKiL,aAAexG,EAAS,KACxEuG,EAAShL,EAAKiL,YA9BvB5C,KACAvI,EAAcwI,iBAAiB,UAAWD,IAC1C3I,EAAK+B,YAAY,GAAK,kBAAMsK,EAAe,SAEtC7H,UAAYA,QAEZiB,SAAW,SAACN,EAAQkI,EAAQ1H,OAC5Be,EAAI1G,EAAKyB,MAAME,MAAM,EAAGzB,IAAc6F,MAAMZ,GAAUG,EAAKa,OAAOhB,EAAQQ,IAAY0H,IAAWtJ,IAAWoB,GAC/GkI,EAAwBtJ,EAAS/D,EAAKgK,GAAG1E,EAAM,CAAC6E,SAAUhG,EAAgBK,UAAWkC,EAAGwD,UAAW,OAAQD,KAAMnJ,IAAUgE,EAAW4B,GAA7HlC,GAAUkC,SAGhBP,OAAS,SAAChB,EAAQQ,EAAUoI,OAI/BlK,EAFGmK,GADJ7I,EAASxE,EAASwE,GAAQ,IACLlC,MAAM+K,QAC1BpI,EAAKxF,EAAcuL,OAAO,CAACvI,QAAS+B,EAAQU,MAAOF,GAAY,mBAE5DoB,IACHsF,EAAejM,EAAc6B,UAAY2E,GAAuB,CAAChB,IAAK,IAEvE/B,EAAI+B,EAAGC,OAASkI,EAAchJ,EAAQ,GACtCa,EAAGlD,MAAK,GACRyC,EAAOlC,MAAM+K,QAAUA,EACvBhO,EAAKqB,KAAKmL,SAASrH,GAAQ8I,QAAU,EAC9BpK,QAUHb,QAAU,SAASkL,MACnBxJ,UAAUC,OAAQ,KACjBwJ,EAAaxN,EAASuN,GAAW,mBAAmB,IAAM1L,QAAQC,KAAK,kDAAoD/B,EAAM0N,SAAS,UAC1ID,IAAenL,IAElB8J,GADA9J,EAAUmL,GACW9E,aAAa,UAAY,GAC9CoE,GAAkBA,EAAeY,QAAQrL,GACzChD,EAAKsO,IAAItL,EAAS,CAACE,SAAU,UAAWqL,MAAO,OAAQC,UAAW,aAAc3K,EAAG,QACnFM,GAAkBnE,EAAKsO,IAAItL,EAAS,CAACyL,WAAY,eAE3CtM,YAEDa,QAGHkC,QAAU,SAASgJ,UACnBxJ,UAAUC,QACbO,EAAUvE,EAASuN,GAAW,mBAAmB,IAtW5C,SAARQ,MAAQvF,OACHjE,EAAU1E,EAAKmO,cAAc,kCAC5BzJ,KACJA,EAAU1E,EAAKoO,cAAc,QACrBC,UAAUC,IAAI,0BACtB3F,EAAG2B,WAAWiE,aAAa7J,EAASiE,GACpCjE,EAAQ8J,YAAY7F,IAEdjE,EA8VkDwJ,CAAM1L,GAC7D6J,EAAa3H,EAAQmE,aAAa,UAAY,GAC9CwE,gBACA7N,EAAKsO,IAAIpJ,EAASf,EAAiB,CAACjB,SAAU,SAAUyC,SAAU,QAAS2F,OAAQ,OAAQiD,MAAO,OAAQpD,IAAK,EAAG8D,KAAM,EAAGC,MAAO,EAAG7D,OAAQ,GAAK,CAACnI,SAAU,UAAWyC,SAAU,WAAY4I,MAAO,OAAQjD,OAAQ,OAAQH,IAAK,OAAQE,OAAQ,OAAQ4D,KAAM,OAAQC,MAAO,SACxQ/M,MAED+C,QAGH6B,QAAU,SAACoI,EAASC,MACZrI,EAAZA,GAAsB,IACjBoI,SACGpI,EAAQsI,MAAM,IAEtBF,EAAUxO,EAASwO,IACX1I,QAAQ,SAAAtB,WACXkC,EAAIN,EAAQpC,OACT0C,KACNN,EAAQM,GAAGjE,UAAY+B,GAAU4B,EAAQM,GAAG3E,SAG9C0M,EAASA,GAAU,OAGlB/H,EAAGzB,EAFCb,EAA8BqK,EAA9BrK,MAAO2E,EAAuB0F,EAAvB1F,IAAKC,EAAkByF,EAAlBzF,eAChB2F,EAAe,OAEXjI,EAAI,EAAGA,EAAI8H,EAAQxK,OAAQ0C,KAC/BzB,EAAK6D,GAAa0F,EAAQ9H,GAAItC,EAAO2E,EAAKrC,EAAGsC,KACvC2F,EAAavH,KAAKnC,UAEzBmB,EAAQgB,WAARhB,EAAgBuI,IACG,IAAnBF,EAAOnN,SAAqB7B,EAAc6B,UACnCqN,QAGH3C,SAAW,SAACwC,EAASC,MACZzC,EAAbA,GAAwB,IACnBwC,SACGxC,EAAS0C,MAAM,OAEnBE,EAAc5O,EAASwO,GAASK,IAAI,SAAArG,UAAM/I,EAAcuL,OAAO,CACjEvI,QAAS+F,EACTtD,MAAO,WACPI,IAAK,cACLwJ,SAAU,kBAAAhF,GACTtB,EAAGlG,MAAMyM,QAAUjF,EAAKyB,SAAW,IAAM,IACzC/C,EAAGlG,MAAM0M,cAAgBlF,EAAKyB,SAAW,MAAQ,mBAIpDkD,GAAUA,EAAON,IAAMnC,EAAS5E,WAAT4E,EAAiB4C,GAAgB5C,EAAW4C,EAAYF,MAAM,GAC9EE,QAGHvM,QAAQT,EAAKS,cACbkC,QAAQ3C,EAAK2C,cACbtB,OAAS,SAAAC,UAAKD,GAAOC,GAAW,IAANA,EAAUA,EAAIf,SACxC8M,YAAc,kBAAMhN,EAAQgN,aAAa9M,IAE9C1C,EAAcyP,cAAc3K,EAAS,CACpCV,UAAWA,GACXsL,aAAc,+BAAMjC,iBAAmBnN,EAAMoP,cAC7CC,cAAoC,IAAtBxN,EAAKwN,gBAA4B5L,EAC/CnB,QAASA,EACTgI,6DACQ,CAACG,IAAK,EAAG8D,KAAM,EAAGV,MAAOjO,EAAK0P,WAAY1E,OAAQhL,EAAKiL,gBAGhEnL,EAAc6P,SAAS,CAAC9P,SAAU+E,QAC9BgL,EAAyB9P,EAAcgI,SAAS+H,OAAO,SAAAvK,UAAMA,EAAGzF,WAAaG,GAAQsF,EAAGzF,WAAa+E,IACzGgL,EAAuBzJ,QAAQ,SAAAb,UAAMA,EAAGwE,QAAO,GAAM,KAErDvF,EAASzE,EAAcuL,OAAO,CAC7BtJ,UAAWrC,EAAKoQ,OAAOxL,EAAQ,CAACf,EAAG,oBAAQqJ,EAAe,IAAgB,CACzErJ,EAAG,oBAAOqJ,EAAe,GAAWW,iBACpCwC,iBAAiB,EACjBpG,KAAM,OACNqG,KAAM,iBACNnG,SAAU,IACVsB,SAAU,uBACLyB,EAAc,KACbpJ,EAAQJ,EACRI,IACHX,GAAU0B,GACVD,EAAOf,EAAIf,GAEZc,GAAOgB,EAAOf,EAAGC,GACjBnB,KACA8I,IAAa1H,GAAU0H,EAAShB,OAInC1B,cAAe,uBAAA0B,OACVtJ,eAAea,iBAGnBb,eAAea,cAAe,EAC1B+E,EAAS,KACRX,EAAOhG,EAAcgI,SAAS+H,OAAO,SAAAvK,WAAQA,EAAG+B,MACpDZ,EAAQN,QAAQ,SAAAb,GACVA,EAAGrD,KAAKiF,iBACZpB,EAAKK,QAAQ,SAAA8J,MACRA,EAAM5I,IAAIvC,SAASQ,EAAGxC,SAAU,KAC/BgG,EAAIxD,EAAGrD,KACX6G,EAAE5B,gBAAkB+I,EAAM5I,IAC1B/B,EAAGrD,KAAO,KACVqD,EAAG4K,KAAKpH,EAAGxD,EAAGvD,oBAMfgB,EAAQoH,EAAKnH,WACjB2J,EAAuB5J,GAASA,EAAMoN,KAAOpN,EAAMqN,IAAIpO,MACvD0K,EAAwBlK,EACxB8B,EAAOf,EAAI,EACPM,IACuB,IAA1B/D,EAAcoN,UAAkBtI,EAAQjC,MAAM0C,SAAW,YACzDT,EAAQV,UAAY,EACM,IAA1BpE,EAAcoN,UAAkBtI,EAAQjC,MAAM0C,SAAW,YAG3DuC,UAAW,mBAAAuC,GACVA,EAAKpI,UAAUsO,aACflG,EAAK7C,aAAa6C,EAAK5E,MAAOgI,gBAAkB9I,GAChDkI,GAAwB9J,GAAUsH,GAClC7F,EAAOf,GAAKiB,IAAeC,EAC3BnB,GAAOgB,EAAOf,GACTwI,IACJY,IAAyBvJ,GAAmB,GAC5C+G,EAAKpI,UAAUsB,SAAS3D,EAAKyB,MAAME,MAAM,EAAG,EAAGqL,EAAwBjI,GAAS0F,EAAKxE,OAElFgH,IACHxC,EAAK9G,UAAY,KACjB8G,EAAK5H,UAEN1B,eAAea,cAAe,GAE/B4O,GAAI,iBACJzQ,SAAUG,EACVuQ,qBAAqB,EACrBhL,MAAO,EACP+F,iBAAkB,KAClB3F,IAAK,sBAAM4H,gBAAkB9I,GAC7B+L,gBAAiB,2BAChBlO,EAAQ8F,QACR0E,GAAUA,EAAO9H,IAElBjC,MAAOc,IAAkB,SAGrBkJ,OAAS,SAAS5I,UAClBC,UAAUC,SAEbI,GADAZ,EAAiBM,GAAS,KACElC,EAAKwC,OAAU,EAC3CF,EAAOkM,cAActM,IAEfI,EAAOvB,WAAauB,EAAOvB,WAAW6G,WAAa,GAG3DtF,EAAOvB,aAAeuB,EAAOvB,WAAWf,KAAK0H,KAAO1H,EAAK0H,MAAQnJ,QAE5DsB,cAAgByC,EAErBtC,EAAKwE,SAAW5E,KAAK4E,SAAyB,IAAjBxE,EAAKwE,QAAmB,SAAWuC,EAAgB,iBAAmBA,EAAgB,OAAS/G,EAAKwE,QAAS,CAAC4C,eAAgBpH,EAAKoH,eAAgB1H,SAAS,IACzLM,EAAKoK,UAAYxK,KAAKwK,UAA2B,IAAlBpK,EAAKoK,SAAoB,iBAAmBpK,EAAKoK,UAEhFuD,EAAuBzJ,QAAQ,SAAAb,GAC9BA,EAAGrD,KAAKpC,SAAW+E,EACnBU,EAAGwE,QAAO,GAAO,GACjBxE,EAAG4K,KAAK5K,EAAGrD,KAAMqD,EAAGvD,kBAGhB0B,OAAS,SAASU,EAAOuM,UACzBtM,UAAUC,UACPZ,IAAWU,IACZA,GACHI,EAAOvB,YAAcuB,EAAOvB,WAAWpB,QACvC4C,GAAYhC,EAAWiC,GACvBnC,EAAQ8F,SACRqE,EAAmB3M,EAAckN,oBACbP,EAAiBkE,WACrClN,EAAS3D,EAAciO,QAAQ,CAC9B6C,gBAAgB,EAChBC,KAAM,qBACNC,UAAU,EACVC,aAAa,EACbC,UAAW,4BAAM9M,IAAW1B,OAEtByO,OAASvQ,EAAeP,EAAQ,sBAAsB,GAA4B,IAAtBuQ,KAEnEjN,EAAOwN,OAAO7O,OACdqB,EAAOrB,OACPqB,EAAS,EACTgJ,GAAoBA,EAAiByE,SACrC3M,EAAOlB,WAAab,EAAWiC,EAAQF,EAAOgB,QAAUhB,EAAOoB,IAAMpB,EAAOgB,OAC5E1C,GAAU0B,KAGL1C,QAEC4B,QAGLrB,KAAOP,KAAKiI,OAAS,WACzB9E,EAAKvB,QAAO,GACZZ,GAAU0B,GACVA,EAAOnC,eACHmE,GAAYE,GAAW,IAAI0K,OAAO9E,GAAY,IACjDtF,EAAIR,EAASlC,OACP0C,KACNR,EAASQ,GAAG3E,OAEbtC,EAAcyP,cAAc3K,GAC5B9E,EAAcsR,oBAAoB,UAAW/I,IAC7CvI,EAAcsR,oBAAoB,UAAWxJ,IAC7ChD,EAAQjC,MAAM+K,QAAUnB,EACxB7J,EAAQC,MAAM+K,QAAUlB,MACpBmD,EAAW7P,EAAc6P,SAAS,IACtCA,GAAYA,EAAS9P,WAAa+E,GAAW9E,EAAc6P,SAAS,CAAC9P,SAAUG,IAC/EgF,EAAKqM,YAAcvR,EAAckN,iBAAgB,GACjDsE,cAAchF,GACd/L,EAAgB,KAChB4M,GAAkBA,EAAeoE,aACjCnR,EAAMuC,MAAM6O,eAAe,UAC3BxR,EAAKoR,oBAAoB,UAAW1M,UAGhC/C,QAAU,SAAC8P,EAAMjO,UAAUe,EAAO5C,QAAQ8P,EAAMjO,IAEjDwJ,SACEqE,WAAavR,EAAckN,iBAAoC,IAApBA,EAA2B,CAAE8D,UAAU,EAAMpO,SAAUmB,GAAkBnB,GAAYsK,IAGtIlN,EAAcgP,OAAO7M,sBAEAjC,EAAK0R,iBAAiBtR,IAAWV,EAAKsO,IAAI,CAAC5N,EAAOD,GAAS,CAACwR,eAAgB,SAGjG3R,EAAKsI,iBAAiB,UAAW5D,IAEjC4H,EAAasF,YAAYvP,GAAgB,KAErB,YAApBnC,EAAK2R,YAA4BhK,sBAAsB,kBAAM/H,EAAc6B,YAyC7Ed,EAAeiR,QAAU,SACzBjR,EAAewK,OAAS,SAAApJ,UAAS1B,GAAiB0B,GAAQ1B,EAAcmC,YAAcrC,EAAS4B,EAAKS,SAAS,GAAMnC,EAAgB,IAAIM,EAAeoB,IACtJpB,EAAekR,IAAM,kBAAMxR,GAE3Bd,KAAcC,EAAKC,eAAekB"}