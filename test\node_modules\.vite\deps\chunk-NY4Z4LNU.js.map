{"version": 3, "sources": ["../../three/examples/jsm/shaders/CopyShader.js"], "sourcesContent": ["/**\n * @module CopyShader\n * @three_import import { CopyShader } from 'three/addons/shaders/CopyShader.js';\n */\n\n/**\n * Full-screen copy shader pass.\n *\n * @constant\n * @type {ShaderMaterial~Shader}\n */\nconst CopyShader = {\n\n\tname: 'CopyShader',\n\n\tuniforms: {\n\n\t\t'tDiffuse': { value: null },\n\t\t'opacity': { value: 1.0 }\n\n\t},\n\n\tvertexShader: /* glsl */`\n\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}`,\n\n\tfragmentShader: /* glsl */`\n\n\t\tuniform float opacity;\n\n\t\tuniform sampler2D tDiffuse;\n\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvec4 texel = texture2D( tDiffuse, vUv );\n\t\t\tgl_FragColor = opacity * texel;\n\n\n\t\t}`\n\n};\n\nexport { CopyShader };\n"], "mappings": ";AAWA,IAAM,aAAa;AAAA,EAElB,MAAM;AAAA,EAEN,UAAU;AAAA,IAET,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,WAAW,EAAE,OAAO,EAAI;AAAA,EAEzB;AAAA,EAEA;AAAA;AAAA,IAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWxB;AAAA;AAAA,IAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgB3B;", "names": []}