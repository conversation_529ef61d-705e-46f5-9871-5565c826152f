/*!
 * PixiPlugin 3.13.0
 * https://gsap.com
 * 
 * @license Copyright 2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license.
 * @author: <PERSON>, <EMAIL>
 */

!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((t=t||self).window=t.window||{})}(this,function(r){"use strict";function l(){return"undefined"!=typeof window}function m(){return e||l()&&(e=window.gsap)&&e.registerPlugin&&e}function n(t){return"function"==typeof t}function o(t){return console.warn(t)}function t(t){return n(p[t])?p[t]:p.filters[t]}function u(t,r){var i,o,e=[],n=0,s=0;for(i=0;i<4;i++){for(o=0;o<5;o++)s=4===o?t[n+4]:0,e[n+o]=t[n]*r[o]+t[n+1]*r[o+5]+t[n+2]*r[o+10]+t[n+3]*r[o+15]+s;n+=5}return e}function v(t,r){var i=1-r,o=i*M,e=i*_,n=i*C;return u([o+r,e,n,0,0,o,e+r,n,0,0,o,e,n+r,0,0,0,0,0,1,0],t)}function w(t,r,i){var o=a(r),e=o[0]/255,n=o[1]/255,s=o[2]/255,l=1-i;return u([l+i*e*M,i*e*_,i*e*C,0,0,i*n*M,l+i*n*_,i*n*C,0,0,i*s*M,i*s*_,l+i*s*C,0,0,0,0,0,1,0],t)}function x(t,r){r*=Math.PI/180;var i=Math.cos(r),o=Math.sin(r);return u([M+i*(1-M)+o*-M,_+i*-_+o*-_,C+i*-C+o*(1-C),0,0,M+i*-M+.143*o,_+i*(1-_)+.14*o,C+i*-C+-.283*o,0,0,M+i*-M+o*-(1-M),_+i*-_+o*_,C+i*(1-C)+o*C,0,0,0,0,0,1,0,0,0,0,0,1],t)}function y(t,r){return u([r,0,0,0,.5*(1-r),0,r,0,0,.5*(1-r),0,0,r,0,.5*(1-r),0,0,0,1,0],t)}function z(r,i){var e,n=t(i),s=r.filters||[],l=s.length;for(n||o(i+" not found. PixiPlugin.registerPIXI(PIXI)");-1<--l;)if(s[l]instanceof n)return s[l];return e=new n,"BlurFilter"===i&&(b?e.strength=0:e.blur=0),r.filters=[].concat(s,[e]),e}function A(t,r,i,o){r.add(i,t,i[t],o[t]),r._props.push(t)}function B(r,i){var o=new(t("ColorMatrixFilter"));return o.matrix=i,o.brightness(r,!0),o.matrix}function E(t,r,i){var o,e,n,s=z(t,"ColorMatrixFilter"),l=t._gsColorMatrixFilter=t._gsColorMatrixFilter||function _copy(t){var r,i={};for(r in t)i[r]=t[r];return i}(X),u=r.combineCMF&&!("colorMatrixFilter"in r&&!r.colorMatrixFilter);n=s.matrix,r.resolution&&(s.resolution=r.resolution),r.matrix&&r.matrix.length===n.length?(e=r.matrix,1!==l.contrast&&A("contrast",i,l,X),l.hue&&A("hue",i,l,X),1!==l.brightness&&A("brightness",i,l,X),l.colorizeAmount&&(A("colorize",i,l,X),A("colorizeAmount",i,l,X)),1!==l.saturation&&A("saturation",i,l,X)):(e=h.slice(),null!=r.contrast?(e=y(e,+r.contrast),A("contrast",i,l,r)):1!==l.contrast&&(u?e=y(e,l.contrast):A("contrast",i,l,X)),null!=r.hue?(e=x(e,+r.hue),A("hue",i,l,r)):l.hue&&(u?e=x(e,l.hue):A("hue",i,l,X)),null!=r.brightness?(e=B(+r.brightness,e),A("brightness",i,l,r)):1!==l.brightness&&(u?e=B(l.brightness,e):A("brightness",i,l,X)),null!=r.colorize?(r.colorizeAmount="colorizeAmount"in r?+r.colorizeAmount:1,e=w(e,r.colorize,r.colorizeAmount),A("colorize",i,l,r),A("colorizeAmount",i,l,r)):l.colorizeAmount&&(u?e=w(e,l.colorize,l.colorizeAmount):(A("colorize",i,l,X),A("colorizeAmount",i,l,X))),null!=r.saturation?(e=v(e,+r.saturation),A("saturation",i,l,r)):1!==l.saturation&&(u?e=v(e,l.saturation):A("saturation",i,l,X))),o=e.length;for(;-1<--o;)e[o]!==n[o]&&i.add(n,o,n[o],e[o],"colorMatrixFilter");i._props.push("colorMatrixFilter")}function F(t,r){var i=r.t,o=r.p,e=r.color;(0,r.set)(i,o,e[0]<<16|e[1]<<8|e[2])}function G(t,r){var i=r.g;b?(i.fill(),i.stroke()):i&&(i.dirty++,i.clearDirty++)}function H(t,r){r.t.visible=!!r.t.alpha}function I(t,r,i,o){var e=t[r],s=a(n(e)?t[r.indexOf("set")||!n(t["get"+r.substr(3)])?r:"get"+r.substr(3)]():e),l=a(i);o._pt=new d(o._pt,t,r,0,0,F,{t:t,p:r,color:s,set:c(t,r)}),o.add(s,0,s[0],l[0]),o.add(s,1,s[1],l[1]),o.add(s,2,s[2],l[2])}function O(t){return"string"==typeof t}function P(t){return O(t)&&"="===t.charAt(1)?t.substr(0,2)+parseFloat(t.substr(2))*N:t*N}function Q(t,r){return r.set(r.t,r.p,1===t?r.e:Math.round(1e5*(r.s+r.c*t))/1e5,r)}function R(t,r,i,o,e,n){var s,l,u=360*(n?N:1),a=O(e),c=a&&"="===e.charAt(1)?+(e.charAt(0)+"1"):0,f=parseFloat(c?e.substr(2):e)*(n?N:1),h=c?f*c:f-o,p=o+h;return a&&("short"===(s=e.split("_")[1])&&(h%=u)!==h%(u/2)&&(h+=h<0?u:-u),"cw"===s&&h<0?h=(h+1e10*u)%u-~~(h/u)*u:"ccw"===s&&0<h&&(h=(h-1e10*u)%u-~~(h/u)*u)),t._pt=l=new d(t._pt,r,i,o,h,Q),l.e=p,l}function S(){if(!i){e=m();var t=(p=i=p||l()&&window.PIXI)&&p.VERSION&&parseFloat(p.VERSION.split(".")[0])||0;g=4===t,b=8<=t,a=function _splitColor(t){return e.utils.splitColor("0x"===(t+"").substr(0,2)?"#"+t.substr(2):t)}}}var e,a,i,p,d,c,g,b,s,f,h=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],M=.212671,_=.71516,C=.072169,X={contrast:1,saturation:1,colorizeAmount:0,colorize:"rgb(255,255,255)",hue:0,brightness:1},k={tint:1,lineColor:1,fillColor:1,strokeColor:1},Y="position,scale,skew,pivot,anchor,tilePosition,tileScale".split(","),j={x:"position",y:"position",tileX:"tilePosition",tileY:"tilePosition"},D={colorMatrixFilter:1,saturation:1,contrast:1,hue:1,colorize:1,colorizeAmount:1,brightness:1,combineCMF:1},N=Math.PI/180;for(s=0;s<Y.length;s++)f=Y[s],j[f+"X"]=f,j[f+"Y"]=f;var V={version:"3.13.0",name:"pixi",register:function register(t,r,i){e=t,d=i,c=r.getSetter,S()},headless:!0,registerPIXI:function registerPIXI(t){p=t},init:function init(t,r){if(p||S(),!p)return o("PIXI was not found. PixiPlugin.registerPIXI(PIXI);"),!1;var i,e,n,s,l,u,a,c,f,h;for(u in r){if(i=j[u],n=r[u],i)e=~u.charAt(u.length-1).toLowerCase().indexOf("x")?"x":"y",this.add(t[i],e,t[i][e],"skew"===i?P(n):n,0,0,0,0,0,1);else if("scale"===u||"anchor"===u||"pivot"===u||"tileScale"===u)this.add(t[u],"x",t[u].x,n),this.add(t[u],"y",t[u].y,n);else if("rotation"===u||"angle"===u)R(this,t,u,t[u],n,"rotation"===u);else if(D[u])s||(E(t,r.colorMatrixFilter||r,this),s=!0);else if("blur"===u||"blurX"===u||"blurY"===u||"blurPadding"===u){if(l=z(t,"BlurFilter"),this.add(l,u,l[u],n),0!==r.blurPadding)for(a=r.blurPadding||2*Math.max(l[u],n),c=t.filters.length;-1<--c;)t.filters[c].padding=Math.max(t.filters[c].padding,a)}else if(k[u])if(("lineColor"===u||"fillColor"===u||"strokeColor"===u)&&t instanceof p.Graphics){f="fillStyle"in t?[t]:(t.geometry||t).graphicsData,h=u.substr(0,u.length-5),b&&"line"===h&&(h="stroke"),this._pt=new d(this._pt,t,u,0,0,G,{g:t.geometry||t}),c=f.length;for(;-1<--c;)I(g?f[c]:f[c][h+"Style"],g?u:"color",n,this)}else I(t,u,n,this);else"autoAlpha"===u?(this._pt=new d(this._pt,t,"visible",0,0,H),this.add(t,"alpha",t.alpha,n),this._props.push("alpha","visible")):"resolution"!==u&&this.add(t,u,"get",n);this._props.push(u)}}};m()&&e.registerPlugin(V),r.PixiPlugin=V,r.default=V;if (typeof(window)==="undefined"||window!==r){Object.defineProperty(r,"__esModule",{value:!0})} else {delete r.default}});

