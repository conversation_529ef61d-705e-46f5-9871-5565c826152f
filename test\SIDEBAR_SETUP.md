# Vue 3 Sidebar 组件安装指南

## 项目结构

已创建的文件结构：
```
test/src/components/sidebar/
├── Sidebar.vue          # 主容器组件
├── SidebarBody.vue      # 侧边栏主体
├── SidebarLink.vue      # 侧边栏链接
├── SidebarDemo.vue      # 演示组件
├── Logo.vue             # Logo 组件
├── LogoIcon.vue         # Logo 图标
├── Dashboard.vue        # 仪表板组件
├── index.js             # 导出文件
└── README.md            # 组件说明
```

## 当前状态

✅ **已完成的工作：**
1. 将 React Sidebar 组件完全转换为 Vue 3 组件
2. 使用 Vue 3 Composition API
3. 实现响应式状态管理（provide/inject）
4. 添加 Vue Transition 动画效果
5. 保持原有的 Tailwind CSS 样式
6. 创建完整的组件演示

## 依赖要求

### 当前项目状态检查

你的项目是一个标准的 Vue 3 项目，但缺少以下依赖：

❌ **缺少 Tailwind CSS** - 组件使用了 Tailwind 样式类
❌ **缺少 TypeScript** - 虽然组件是 JavaScript，但建议使用 TypeScript

## 安装步骤

### 1. 安装 Tailwind CSS

```bash
# 安装 Tailwind CSS
npm install -D tailwindcss postcss autoprefixer

# 初始化 Tailwind 配置
npx tailwindcss init -p
```

### 2. 配置 Tailwind CSS

编辑 `tailwind.config.js`：
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

### 3. 添加 Tailwind 指令

在 `src/assets/main.css` 中添加：
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### 4. （可选）安装 TypeScript

```bash
# 安装 TypeScript
npm install -D typescript @vue/tsconfig

# 创建 tsconfig.json
{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

## 使用方法

### 基本使用

```vue
<template>
  <SidebarDemo />
</template>

<script setup>
import SidebarDemo from '@/components/sidebar/SidebarDemo.vue'
</script>
```

### 自定义使用

```vue
<template>
  <Sidebar v-model:open="isOpen">
    <SidebarBody>
      <!-- 你的内容 -->
    </SidebarBody>
  </Sidebar>
</template>

<script setup>
import { ref } from 'vue'
import { Sidebar, SidebarBody } from '@/components/sidebar'

const isOpen = ref(false)
</script>
```

## 特性说明

1. **响应式设计**：桌面端悬停展开，移动端点击切换
2. **平滑动画**：使用 Vue Transition 实现
3. **状态管理**：使用 provide/inject 在组件间共享状态
4. **无外部依赖**：除了 Vue 3 和 Tailwind CSS
5. **易于定制**：可以轻松修改样式和行为

## 下一步

1. 运行 `npm run dev` 查看效果
2. 根据需要自定义样式和功能
3. 集成到你的实际项目中

## 注意事项

- 如果不想使用 Tailwind CSS，需要手动编写对应的 CSS 样式
- 图标目前使用 SVG 字符串，可以替换为图标库（如 lucide-vue）
- 链接使用普通 `<a>` 标签，可以替换为 Vue Router 的 `<router-link>`
