{"version": 3, "sources": ["../../three/examples/jsm/postprocessing/ShaderPass.js", "../../three/examples/jsm/postprocessing/MaskPass.js", "../../three/examples/jsm/postprocessing/EffectComposer.js"], "sourcesContent": ["import {\n\tShaderMaterial,\n\tUniformsUtils\n} from 'three';\nimport { Pass, FullScreenQuad } from './Pass.js';\n\n/**\n * This pass can be used to create a post processing effect\n * with a raw GLSL shader object. Useful for implementing custom\n * effects.\n *\n * ```js\n * const fxaaPass = new ShaderPass( FXAAShader );\n * composer.addPass( fxaaPass );\n * ```\n *\n * @augments Pass\n * @three_import import { ShaderPass } from 'three/addons/postprocessing/ShaderPass.js';\n */\nclass ShaderPass extends Pass {\n\n\t/**\n\t * Constructs a new shader pass.\n\t *\n\t * @param {Object|ShaderMaterial} [shader] - A shader object holding vertex and fragment shader as well as\n\t * defines and uniforms. It's also valid to pass a custom shader material.\n\t * @param {string} [textureID='tDiffuse'] - The name of the texture uniform that should sample\n\t * the read buffer.\n\t */\n\tconstructor( shader, textureID = 'tDiffuse' ) {\n\n\t\tsuper();\n\n\t\t/**\n\t\t * The name of the texture uniform that should sample the read buffer.\n\t\t *\n\t\t * @type {string}\n\t\t * @default 'tDiffuse'\n\t\t */\n\t\tthis.textureID = textureID;\n\n\t\t/**\n\t\t * The pass uniforms.\n\t\t *\n\t\t * @type {?Object}\n\t\t */\n\t\tthis.uniforms = null;\n\n\t\t/**\n\t\t * The pass material.\n\t\t *\n\t\t * @type {?ShaderMaterial}\n\t\t */\n\t\tthis.material = null;\n\n\t\tif ( shader instanceof ShaderMaterial ) {\n\n\t\t\tthis.uniforms = shader.uniforms;\n\n\t\t\tthis.material = shader;\n\n\t\t} else if ( shader ) {\n\n\t\t\tthis.uniforms = UniformsUtils.clone( shader.uniforms );\n\n\t\t\tthis.material = new ShaderMaterial( {\n\n\t\t\t\tname: ( shader.name !== undefined ) ? shader.name : 'unspecified',\n\t\t\t\tdefines: Object.assign( {}, shader.defines ),\n\t\t\t\tuniforms: this.uniforms,\n\t\t\t\tvertexShader: shader.vertexShader,\n\t\t\t\tfragmentShader: shader.fragmentShader\n\n\t\t\t} );\n\n\t\t}\n\n\t\t// internals\n\n\t\tthis._fsQuad = new FullScreenQuad( this.material );\n\n\t}\n\n\t/**\n\t * Performs the shader pass.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} writeBuffer - The write buffer. This buffer is intended as the rendering\n\t * destination for the pass.\n\t * @param {WebGLRenderTarget} readBuffer - The read buffer. The pass can access the result from the\n\t * previous pass from this buffer.\n\t * @param {number} deltaTime - The delta time in seconds.\n\t * @param {boolean} maskActive - Whether masking is active or not.\n\t */\n\trender( renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */ ) {\n\n\t\tif ( this.uniforms[ this.textureID ] ) {\n\n\t\t\tthis.uniforms[ this.textureID ].value = readBuffer.texture;\n\n\t\t}\n\n\t\tthis._fsQuad.material = this.material;\n\n\t\tif ( this.renderToScreen ) {\n\n\t\t\trenderer.setRenderTarget( null );\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t} else {\n\n\t\t\trenderer.setRenderTarget( writeBuffer );\n\t\t\t// TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n\t\t\tif ( this.clear ) renderer.clear( renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil );\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Frees the GPU-related resources allocated by this instance. Call this\n\t * method whenever the pass is no longer used in your app.\n\t */\n\tdispose() {\n\n\t\tthis.material.dispose();\n\n\t\tthis._fsQuad.dispose();\n\n\t}\n\n}\n\nexport { ShaderPass };\n", "import { Pass } from './Pass.js';\n\n/**\n * This pass can be used to define a mask during post processing.\n * Meaning only areas of subsequent post processing are affected\n * which lie in the masking area of this pass. Internally, the masking\n * is implemented with the stencil buffer.\n *\n * ```js\n * const maskPass = new MaskPass( scene, camera );\n * composer.addPass( maskPass );\n * ```\n *\n * @augments Pass\n * @three_import import { MaskPass } from 'three/addons/postprocessing/MaskPass.js';\n */\nclass MaskPass extends Pass {\n\n\t/**\n\t * Constructs a new mask pass.\n\t *\n\t * @param {Scene} scene - The 3D objects in this scene will define the mask.\n\t * @param {Camera} camera - The camera.\n\t */\n\tconstructor( scene, camera ) {\n\n\t\tsuper();\n\n\t\t/**\n\t\t * The scene that defines the mask.\n\t\t *\n\t\t * @type {Scene}\n\t\t */\n\t\tthis.scene = scene;\n\n\t\t/**\n\t\t * The camera.\n\t\t *\n\t\t * @type {Camera}\n\t\t */\n\t\tthis.camera = camera;\n\n\t\t/**\n\t\t * Overwritten to perform a clear operation by default.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.clear = true;\n\n\t\t/**\n\t\t * Overwritten to disable the swap.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.needsSwap = false;\n\n\t\t/**\n\t\t * Whether to inverse the mask or not.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.inverse = false;\n\n\t}\n\n\t/**\n\t * Performs a mask pass with the configured scene and camera.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} writeBuffer - The write buffer. This buffer is intended as the rendering\n\t * destination for the pass.\n\t * @param {WebGLRenderTarget} readBuffer - The read buffer. The pass can access the result from the\n\t * previous pass from this buffer.\n\t * @param {number} deltaTime - The delta time in seconds.\n\t * @param {boolean} maskActive - Whether masking is active or not.\n\t */\n\trender( renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */ ) {\n\n\t\tconst context = renderer.getContext();\n\t\tconst state = renderer.state;\n\n\t\t// don't update color or depth\n\n\t\tstate.buffers.color.setMask( false );\n\t\tstate.buffers.depth.setMask( false );\n\n\t\t// lock buffers\n\n\t\tstate.buffers.color.setLocked( true );\n\t\tstate.buffers.depth.setLocked( true );\n\n\t\t// set up stencil\n\n\t\tlet writeValue, clearValue;\n\n\t\tif ( this.inverse ) {\n\n\t\t\twriteValue = 0;\n\t\t\tclearValue = 1;\n\n\t\t} else {\n\n\t\t\twriteValue = 1;\n\t\t\tclearValue = 0;\n\n\t\t}\n\n\t\tstate.buffers.stencil.setTest( true );\n\t\tstate.buffers.stencil.setOp( context.REPLACE, context.REPLACE, context.REPLACE );\n\t\tstate.buffers.stencil.setFunc( context.ALWAYS, writeValue, 0xffffffff );\n\t\tstate.buffers.stencil.setClear( clearValue );\n\t\tstate.buffers.stencil.setLocked( true );\n\n\t\t// draw into the stencil buffer\n\n\t\trenderer.setRenderTarget( readBuffer );\n\t\tif ( this.clear ) renderer.clear();\n\t\trenderer.render( this.scene, this.camera );\n\n\t\trenderer.setRenderTarget( writeBuffer );\n\t\tif ( this.clear ) renderer.clear();\n\t\trenderer.render( this.scene, this.camera );\n\n\t\t// unlock color and depth buffer and make them writable for subsequent rendering/clearing\n\n\t\tstate.buffers.color.setLocked( false );\n\t\tstate.buffers.depth.setLocked( false );\n\n\t\tstate.buffers.color.setMask( true );\n\t\tstate.buffers.depth.setMask( true );\n\n\t\t// only render where stencil is set to 1\n\n\t\tstate.buffers.stencil.setLocked( false );\n\t\tstate.buffers.stencil.setFunc( context.EQUAL, 1, 0xffffffff ); // draw if == 1\n\t\tstate.buffers.stencil.setOp( context.KEEP, context.KEEP, context.KEEP );\n\t\tstate.buffers.stencil.setLocked( true );\n\n\t}\n\n}\n\n/**\n * This pass can be used to clear a mask previously defined with {@link MaskPass}.\n *\n * ```js\n * const clearPass = new ClearMaskPass();\n * composer.addPass( clearPass );\n * ```\n *\n * @augments Pass\n */\nclass ClearMaskPass extends Pass {\n\n\t/**\n\t * Constructs a new clear mask pass.\n\t */\n\tconstructor() {\n\n\t\tsuper();\n\n\t\t/**\n\t\t * Overwritten to disable the swap.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.needsSwap = false;\n\n\t}\n\n\t/**\n\t * Performs the clear of the currently defined mask.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} writeBuffer - The write buffer. This buffer is intended as the rendering\n\t * destination for the pass.\n\t * @param {WebGLRenderTarget} readBuffer - The read buffer. The pass can access the result from the\n\t * previous pass from this buffer.\n\t * @param {number} deltaTime - The delta time in seconds.\n\t * @param {boolean} maskActive - Whether masking is active or not.\n\t */\n\trender( renderer /*, writeBuffer, readBuffer, deltaTime, maskActive */ ) {\n\n\t\trenderer.state.buffers.stencil.setLocked( false );\n\t\trenderer.state.buffers.stencil.setTest( false );\n\n\t}\n\n}\n\nexport { MaskPass, ClearMaskPass };\n", "import {\n\tClock,\n\tHalfFloatType,\n\tNoBlending,\n\tVector2,\n\tWebGLRenderTarget\n} from 'three';\nimport { CopyShader } from '../shaders/CopyShader.js';\nimport { ShaderPass } from './ShaderPass.js';\nimport { ClearMaskPass, MaskPass } from './MaskPass.js';\n\n/**\n * Used to implement post-processing effects in three.js.\n * The class manages a chain of post-processing passes to produce the final visual result.\n * Post-processing passes are executed in order of their addition/insertion.\n * The last pass is automatically rendered to screen.\n *\n * This module can only be used with {@link WebGLRenderer}.\n *\n * ```js\n * const composer = new EffectComposer( renderer );\n *\n * // adding some passes\n * const renderPass = new RenderPass( scene, camera );\n * composer.addPass( renderPass );\n *\n * const glitchPass = new GlitchPass();\n * composer.addPass( glitchPass );\n *\n * const outputPass = new OutputPass()\n * composer.addPass( outputPass );\n *\n * function animate() {\n *\n * \tcomposer.render(); // instead of renderer.render()\n *\n * }\n * ```\n *\n * @three_import import { EffectComposer } from 'three/addons/postprocessing/EffectComposer.js';\n */\nclass EffectComposer {\n\n\t/**\n\t * Constructs a new effect composer.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} [renderTarget] - This render target and a clone will\n\t * be used as the internal read and write buffers. If not given, the composer creates\n\t * the buffers automatically.\n\t */\n\tconstructor( renderer, renderTarget ) {\n\n\t\t/**\n\t\t * The renderer.\n\t\t *\n\t\t * @type {WebGLRenderer}\n\t\t */\n\t\tthis.renderer = renderer;\n\n\t\tthis._pixelRatio = renderer.getPixelRatio();\n\n\t\tif ( renderTarget === undefined ) {\n\n\t\t\tconst size = renderer.getSize( new Vector2() );\n\t\t\tthis._width = size.width;\n\t\t\tthis._height = size.height;\n\n\t\t\trenderTarget = new WebGLRenderTarget( this._width * this._pixelRatio, this._height * this._pixelRatio, { type: HalfFloatType } );\n\t\t\trenderTarget.texture.name = 'EffectComposer.rt1';\n\n\t\t} else {\n\n\t\t\tthis._width = renderTarget.width;\n\t\t\tthis._height = renderTarget.height;\n\n\t\t}\n\n\t\tthis.renderTarget1 = renderTarget;\n\t\tthis.renderTarget2 = renderTarget.clone();\n\t\tthis.renderTarget2.texture.name = 'EffectComposer.rt2';\n\n\t\t/**\n\t\t * A reference to the internal write buffer. Passes usually write\n\t\t * their result into this buffer.\n\t\t *\n\t\t * @type {WebGLRenderTarget}\n\t\t */\n\t\tthis.writeBuffer = this.renderTarget1;\n\n\t\t/**\n\t\t * A reference to the internal read buffer. Passes usually read\n\t\t * the previous render result from this buffer.\n\t\t *\n\t\t * @type {WebGLRenderTarget}\n\t\t */\n\t\tthis.readBuffer = this.renderTarget2;\n\n\t\t/**\n\t\t * Whether the final pass is rendered to the screen (default framebuffer) or not.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.renderToScreen = true;\n\n\t\t/**\n\t\t * An array representing the (ordered) chain of post-processing passes.\n\t\t *\n\t\t * @type {Array<Pass>}\n\t\t */\n\t\tthis.passes = [];\n\n\t\t/**\n\t\t * A copy pass used for internal swap operations.\n\t\t *\n\t\t * @private\n\t\t * @type {ShaderPass}\n\t\t */\n\t\tthis.copyPass = new ShaderPass( CopyShader );\n\t\tthis.copyPass.material.blending = NoBlending;\n\n\t\t/**\n\t\t * The internal clock for managing time data.\n\t\t *\n\t\t * @private\n\t\t * @type {Clock}\n\t\t */\n\t\tthis.clock = new Clock();\n\n\t}\n\n\t/**\n\t * Swaps the internal read/write buffers.\n\t */\n\tswapBuffers() {\n\n\t\tconst tmp = this.readBuffer;\n\t\tthis.readBuffer = this.writeBuffer;\n\t\tthis.writeBuffer = tmp;\n\n\t}\n\n\t/**\n\t * Adds the given pass to the pass chain.\n\t *\n\t * @param {Pass} pass - The pass to add.\n\t */\n\taddPass( pass ) {\n\n\t\tthis.passes.push( pass );\n\t\tpass.setSize( this._width * this._pixelRatio, this._height * this._pixelRatio );\n\n\t}\n\n\t/**\n\t * Inserts the given pass at a given index.\n\t *\n\t * @param {Pass} pass - The pass to insert.\n\t * @param {number} index - The index into the pass chain.\n\t */\n\tinsertPass( pass, index ) {\n\n\t\tthis.passes.splice( index, 0, pass );\n\t\tpass.setSize( this._width * this._pixelRatio, this._height * this._pixelRatio );\n\n\t}\n\n\t/**\n\t * Removes the given pass from the pass chain.\n\t *\n\t * @param {Pass} pass - The pass to remove.\n\t */\n\tremovePass( pass ) {\n\n\t\tconst index = this.passes.indexOf( pass );\n\n\t\tif ( index !== - 1 ) {\n\n\t\t\tthis.passes.splice( index, 1 );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Returns `true` if the pass for the given index is the last enabled pass in the pass chain.\n\t *\n\t * @param {number} passIndex - The pass index.\n\t * @return {boolean} Whether the pass for the given index is the last pass in the pass chain.\n\t */\n\tisLastEnabledPass( passIndex ) {\n\n\t\tfor ( let i = passIndex + 1; i < this.passes.length; i ++ ) {\n\n\t\t\tif ( this.passes[ i ].enabled ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn true;\n\n\t}\n\n\t/**\n\t * Executes all enabled post-processing passes in order to produce the final frame.\n\t *\n\t * @param {number} deltaTime - The delta time in seconds. If not given, the composer computes\n\t * its own time delta value.\n\t */\n\trender( deltaTime ) {\n\n\t\t// deltaTime value is in seconds\n\n\t\tif ( deltaTime === undefined ) {\n\n\t\t\tdeltaTime = this.clock.getDelta();\n\n\t\t}\n\n\t\tconst currentRenderTarget = this.renderer.getRenderTarget();\n\n\t\tlet maskActive = false;\n\n\t\tfor ( let i = 0, il = this.passes.length; i < il; i ++ ) {\n\n\t\t\tconst pass = this.passes[ i ];\n\n\t\t\tif ( pass.enabled === false ) continue;\n\n\t\t\tpass.renderToScreen = ( this.renderToScreen && this.isLastEnabledPass( i ) );\n\t\t\tpass.render( this.renderer, this.writeBuffer, this.readBuffer, deltaTime, maskActive );\n\n\t\t\tif ( pass.needsSwap ) {\n\n\t\t\t\tif ( maskActive ) {\n\n\t\t\t\t\tconst context = this.renderer.getContext();\n\t\t\t\t\tconst stencil = this.renderer.state.buffers.stencil;\n\n\t\t\t\t\t//context.stencilFunc( context.NOTEQUAL, 1, 0xffffffff );\n\t\t\t\t\tstencil.setFunc( context.NOTEQUAL, 1, 0xffffffff );\n\n\t\t\t\t\tthis.copyPass.render( this.renderer, this.writeBuffer, this.readBuffer, deltaTime );\n\n\t\t\t\t\t//context.stencilFunc( context.EQUAL, 1, 0xffffffff );\n\t\t\t\t\tstencil.setFunc( context.EQUAL, 1, 0xffffffff );\n\n\t\t\t\t}\n\n\t\t\t\tthis.swapBuffers();\n\n\t\t\t}\n\n\t\t\tif ( MaskPass !== undefined ) {\n\n\t\t\t\tif ( pass instanceof MaskPass ) {\n\n\t\t\t\t\tmaskActive = true;\n\n\t\t\t\t} else if ( pass instanceof ClearMaskPass ) {\n\n\t\t\t\t\tmaskActive = false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.renderer.setRenderTarget( currentRenderTarget );\n\n\t}\n\n\t/**\n\t * Resets the internal state of the EffectComposer.\n\t *\n\t * @param {WebGLRenderTarget} [renderTarget] - This render target has the same purpose like\n\t * the one from the constructor. If set, it is used to setup the read and write buffers.\n\t */\n\treset( renderTarget ) {\n\n\t\tif ( renderTarget === undefined ) {\n\n\t\t\tconst size = this.renderer.getSize( new Vector2() );\n\t\t\tthis._pixelRatio = this.renderer.getPixelRatio();\n\t\t\tthis._width = size.width;\n\t\t\tthis._height = size.height;\n\n\t\t\trenderTarget = this.renderTarget1.clone();\n\t\t\trenderTarget.setSize( this._width * this._pixelRatio, this._height * this._pixelRatio );\n\n\t\t}\n\n\t\tthis.renderTarget1.dispose();\n\t\tthis.renderTarget2.dispose();\n\t\tthis.renderTarget1 = renderTarget;\n\t\tthis.renderTarget2 = renderTarget.clone();\n\n\t\tthis.writeBuffer = this.renderTarget1;\n\t\tthis.readBuffer = this.renderTarget2;\n\n\t}\n\n\t/**\n\t * Resizes the internal read and write buffers as well as all passes. Similar to {@link WebGLRenderer#setSize},\n\t * this method honors the current pixel ration.\n\t *\n\t * @param {number} width - The width in logical pixels.\n\t * @param {number} height - The height in logical pixels.\n\t */\n\tsetSize( width, height ) {\n\n\t\tthis._width = width;\n\t\tthis._height = height;\n\n\t\tconst effectiveWidth = this._width * this._pixelRatio;\n\t\tconst effectiveHeight = this._height * this._pixelRatio;\n\n\t\tthis.renderTarget1.setSize( effectiveWidth, effectiveHeight );\n\t\tthis.renderTarget2.setSize( effectiveWidth, effectiveHeight );\n\n\t\tfor ( let i = 0; i < this.passes.length; i ++ ) {\n\n\t\t\tthis.passes[ i ].setSize( effectiveWidth, effectiveHeight );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Sets device pixel ratio. This is usually used for HiDPI device to prevent blurring output.\n\t * Setting the pixel ratio will automatically resize the composer.\n\t *\n\t * @param {number} pixelRatio - The pixel ratio to set.\n\t */\n\tsetPixelRatio( pixelRatio ) {\n\n\t\tthis._pixelRatio = pixelRatio;\n\n\t\tthis.setSize( this._width, this._height );\n\n\t}\n\n\t/**\n\t * Frees the GPU-related resources allocated by this instance. Call this\n\t * method whenever the composer is no longer used in your app.\n\t */\n\tdispose() {\n\n\t\tthis.renderTarget1.dispose();\n\t\tthis.renderTarget2.dispose();\n\n\t\tthis.copyPass.dispose();\n\n\t}\n\n}\n\nexport { EffectComposer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAmBA,IAAM,aAAN,cAAyB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU7B,YAAa,QAAQ,YAAY,YAAa;AAE7C,UAAM;AAQN,SAAK,YAAY;AAOjB,SAAK,WAAW;AAOhB,SAAK,WAAW;AAEhB,QAAK,kBAAkB,gBAAiB;AAEvC,WAAK,WAAW,OAAO;AAEvB,WAAK,WAAW;AAAA,IAEjB,WAAY,QAAS;AAEpB,WAAK,WAAW,cAAc,MAAO,OAAO,QAAS;AAErD,WAAK,WAAW,IAAI,eAAgB;AAAA,QAEnC,MAAQ,OAAO,SAAS,SAAc,OAAO,OAAO;AAAA,QACpD,SAAS,OAAO,OAAQ,CAAC,GAAG,OAAO,OAAQ;AAAA,QAC3C,UAAU,KAAK;AAAA,QACf,cAAc,OAAO;AAAA,QACrB,gBAAgB,OAAO;AAAA,MAExB,CAAE;AAAA,IAEH;AAIA,SAAK,UAAU,IAAI,eAAgB,KAAK,QAAS;AAAA,EAElD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAQ,UAAU,aAAa,YAA0C;AAExE,QAAK,KAAK,SAAU,KAAK,SAAU,GAAI;AAEtC,WAAK,SAAU,KAAK,SAAU,EAAE,QAAQ,WAAW;AAAA,IAEpD;AAEA,SAAK,QAAQ,WAAW,KAAK;AAE7B,QAAK,KAAK,gBAAiB;AAE1B,eAAS,gBAAiB,IAAK;AAC/B,WAAK,QAAQ,OAAQ,QAAS;AAAA,IAE/B,OAAO;AAEN,eAAS,gBAAiB,WAAY;AAEtC,UAAK,KAAK,MAAQ,UAAS,MAAO,SAAS,gBAAgB,SAAS,gBAAgB,SAAS,gBAAiB;AAC9G,WAAK,QAAQ,OAAQ,QAAS;AAAA,IAE/B;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AAET,SAAK,SAAS,QAAQ;AAEtB,SAAK,QAAQ,QAAQ;AAAA,EAEtB;AAED;;;ACpHA,IAAM,WAAN,cAAuB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ3B,YAAa,OAAO,QAAS;AAE5B,UAAM;AAON,SAAK,QAAQ;AAOb,SAAK,SAAS;AAQd,SAAK,QAAQ;AAQb,SAAK,YAAY;AAQjB,SAAK,UAAU;AAAA,EAEhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAQ,UAAU,aAAa,YAA0C;AAExE,UAAM,UAAU,SAAS,WAAW;AACpC,UAAM,QAAQ,SAAS;AAIvB,UAAM,QAAQ,MAAM,QAAS,KAAM;AACnC,UAAM,QAAQ,MAAM,QAAS,KAAM;AAInC,UAAM,QAAQ,MAAM,UAAW,IAAK;AACpC,UAAM,QAAQ,MAAM,UAAW,IAAK;AAIpC,QAAI,YAAY;AAEhB,QAAK,KAAK,SAAU;AAEnB,mBAAa;AACb,mBAAa;AAAA,IAEd,OAAO;AAEN,mBAAa;AACb,mBAAa;AAAA,IAEd;AAEA,UAAM,QAAQ,QAAQ,QAAS,IAAK;AACpC,UAAM,QAAQ,QAAQ,MAAO,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAQ;AAC/E,UAAM,QAAQ,QAAQ,QAAS,QAAQ,QAAQ,YAAY,UAAW;AACtE,UAAM,QAAQ,QAAQ,SAAU,UAAW;AAC3C,UAAM,QAAQ,QAAQ,UAAW,IAAK;AAItC,aAAS,gBAAiB,UAAW;AACrC,QAAK,KAAK,MAAQ,UAAS,MAAM;AACjC,aAAS,OAAQ,KAAK,OAAO,KAAK,MAAO;AAEzC,aAAS,gBAAiB,WAAY;AACtC,QAAK,KAAK,MAAQ,UAAS,MAAM;AACjC,aAAS,OAAQ,KAAK,OAAO,KAAK,MAAO;AAIzC,UAAM,QAAQ,MAAM,UAAW,KAAM;AACrC,UAAM,QAAQ,MAAM,UAAW,KAAM;AAErC,UAAM,QAAQ,MAAM,QAAS,IAAK;AAClC,UAAM,QAAQ,MAAM,QAAS,IAAK;AAIlC,UAAM,QAAQ,QAAQ,UAAW,KAAM;AACvC,UAAM,QAAQ,QAAQ,QAAS,QAAQ,OAAO,GAAG,UAAW;AAC5D,UAAM,QAAQ,QAAQ,MAAO,QAAQ,MAAM,QAAQ,MAAM,QAAQ,IAAK;AACtE,UAAM,QAAQ,QAAQ,UAAW,IAAK;AAAA,EAEvC;AAED;AAYA,IAAM,gBAAN,cAA4B,KAAK;AAAA;AAAA;AAAA;AAAA,EAKhC,cAAc;AAEb,UAAM;AAQN,SAAK,YAAY;AAAA,EAElB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAQ,UAAiE;AAExE,aAAS,MAAM,QAAQ,QAAQ,UAAW,KAAM;AAChD,aAAS,MAAM,QAAQ,QAAQ,QAAS,KAAM;AAAA,EAE/C;AAED;;;ACvJA,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUpB,YAAa,UAAU,cAAe;AAOrC,SAAK,WAAW;AAEhB,SAAK,cAAc,SAAS,cAAc;AAE1C,QAAK,iBAAiB,QAAY;AAEjC,YAAM,OAAO,SAAS,QAAS,IAAI,QAAQ,CAAE;AAC7C,WAAK,SAAS,KAAK;AACnB,WAAK,UAAU,KAAK;AAEpB,qBAAe,IAAI,kBAAmB,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,aAAa,EAAE,MAAM,cAAc,CAAE;AAC/H,mBAAa,QAAQ,OAAO;AAAA,IAE7B,OAAO;AAEN,WAAK,SAAS,aAAa;AAC3B,WAAK,UAAU,aAAa;AAAA,IAE7B;AAEA,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,aAAa,MAAM;AACxC,SAAK,cAAc,QAAQ,OAAO;AAQlC,SAAK,cAAc,KAAK;AAQxB,SAAK,aAAa,KAAK;AAQvB,SAAK,iBAAiB;AAOtB,SAAK,SAAS,CAAC;AAQf,SAAK,WAAW,IAAI,WAAY,UAAW;AAC3C,SAAK,SAAS,SAAS,WAAW;AAQlC,SAAK,QAAQ,IAAI,MAAM;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAEb,UAAM,MAAM,KAAK;AACjB,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc;AAAA,EAEpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAS,MAAO;AAEf,SAAK,OAAO,KAAM,IAAK;AACvB,SAAK,QAAS,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,WAAY;AAAA,EAE/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAY,MAAM,OAAQ;AAEzB,SAAK,OAAO,OAAQ,OAAO,GAAG,IAAK;AACnC,SAAK,QAAS,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,WAAY;AAAA,EAE/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAY,MAAO;AAElB,UAAM,QAAQ,KAAK,OAAO,QAAS,IAAK;AAExC,QAAK,UAAU,IAAM;AAEpB,WAAK,OAAO,OAAQ,OAAO,CAAE;AAAA,IAE9B;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAmB,WAAY;AAE9B,aAAU,IAAI,YAAY,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAO;AAE3D,UAAK,KAAK,OAAQ,CAAE,EAAE,SAAU;AAE/B,eAAO;AAAA,MAER;AAAA,IAED;AAEA,WAAO;AAAA,EAER;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAQ,WAAY;AAInB,QAAK,cAAc,QAAY;AAE9B,kBAAY,KAAK,MAAM,SAAS;AAAA,IAEjC;AAEA,UAAM,sBAAsB,KAAK,SAAS,gBAAgB;AAE1D,QAAI,aAAa;AAEjB,aAAU,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAO;AAExD,YAAM,OAAO,KAAK,OAAQ,CAAE;AAE5B,UAAK,KAAK,YAAY,MAAQ;AAE9B,WAAK,iBAAmB,KAAK,kBAAkB,KAAK,kBAAmB,CAAE;AACzE,WAAK,OAAQ,KAAK,UAAU,KAAK,aAAa,KAAK,YAAY,WAAW,UAAW;AAErF,UAAK,KAAK,WAAY;AAErB,YAAK,YAAa;AAEjB,gBAAM,UAAU,KAAK,SAAS,WAAW;AACzC,gBAAM,UAAU,KAAK,SAAS,MAAM,QAAQ;AAG5C,kBAAQ,QAAS,QAAQ,UAAU,GAAG,UAAW;AAEjD,eAAK,SAAS,OAAQ,KAAK,UAAU,KAAK,aAAa,KAAK,YAAY,SAAU;AAGlF,kBAAQ,QAAS,QAAQ,OAAO,GAAG,UAAW;AAAA,QAE/C;AAEA,aAAK,YAAY;AAAA,MAElB;AAEA,UAAK,aAAa,QAAY;AAE7B,YAAK,gBAAgB,UAAW;AAE/B,uBAAa;AAAA,QAEd,WAAY,gBAAgB,eAAgB;AAE3C,uBAAa;AAAA,QAEd;AAAA,MAED;AAAA,IAED;AAEA,SAAK,SAAS,gBAAiB,mBAAoB;AAAA,EAEpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAO,cAAe;AAErB,QAAK,iBAAiB,QAAY;AAEjC,YAAM,OAAO,KAAK,SAAS,QAAS,IAAI,QAAQ,CAAE;AAClD,WAAK,cAAc,KAAK,SAAS,cAAc;AAC/C,WAAK,SAAS,KAAK;AACnB,WAAK,UAAU,KAAK;AAEpB,qBAAe,KAAK,cAAc,MAAM;AACxC,mBAAa,QAAS,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,WAAY;AAAA,IAEvF;AAEA,SAAK,cAAc,QAAQ;AAC3B,SAAK,cAAc,QAAQ;AAC3B,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,aAAa,MAAM;AAExC,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAS,OAAO,QAAS;AAExB,SAAK,SAAS;AACd,SAAK,UAAU;AAEf,UAAM,iBAAiB,KAAK,SAAS,KAAK;AAC1C,UAAM,kBAAkB,KAAK,UAAU,KAAK;AAE5C,SAAK,cAAc,QAAS,gBAAgB,eAAgB;AAC5D,SAAK,cAAc,QAAS,gBAAgB,eAAgB;AAE5D,aAAU,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAO;AAE/C,WAAK,OAAQ,CAAE,EAAE,QAAS,gBAAgB,eAAgB;AAAA,IAE3D;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAe,YAAa;AAE3B,SAAK,cAAc;AAEnB,SAAK,QAAS,KAAK,QAAQ,KAAK,OAAQ;AAAA,EAEzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AAET,SAAK,cAAc,QAAQ;AAC3B,SAAK,cAAc,QAAQ;AAE3B,SAAK,SAAS,QAAQ;AAAA,EAEvB;AAED;", "names": []}