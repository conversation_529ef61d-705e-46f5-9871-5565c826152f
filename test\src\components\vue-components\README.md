# Vue 3 Whiteboard Component

This folder contains the converted Vue 3 version of the React/TypeScript Whiteboard component.

## Files

- `Whiteboard.vue` - Main Vue 3 single-file component using Composition API
- `hero.css` - Stylesheet containing all the component styles
- `README.md` - This documentation file

## Conversion Details

The original React component has been converted to Vue 3 with the following changes:

### React to Vue 3 Syntax Conversion

1. **Hooks to Composition API**:
   - `useRef()` → `ref()`
   - `useState()` → `ref()`
   - `useEffect()` → `onMounted()`, `onUnmounted()`

2. **Template Refs**:
   - React refs like `useRef<HTMLDivElement>(null)` → Vue template refs `ref<HTMLDivElement>()`
   - Access via `.current` → Direct access to `.value`

3. **Event Handling**:
   - React event listeners → Vue lifecycle hooks with manual event listener management
   - Cleanup in `useEffect` return → `onUnmounted()` hook

4. **Reactive Data**:
   - React state variables → Vue reactive refs
   - Direct state updates → `.value` property updates

5. **Template Syntax**:
   - JSX → Vue template syntax
   - `className` → `class`
   - `style={{}}` → `:style="{}"`
   - String interpolation `{variable}` → `{{ variable }}`

## Dependencies Required

To use this component, you'll need to install the following dependencies:

```bash
npm install vue@^3.0.0
npm install three
npm install gsap
```

## Usage

1. Import the component in your Vue 3 application:

```vue
<template>
  <Whiteboard />
</template>

<script setup>
import Whiteboard from './vue-components/Whiteboard.vue'
</script>
```

2. Make sure your Vue 3 project supports TypeScript if you want to use the TypeScript features.

3. The component will automatically handle Three.js scene initialization, GSAP animations, and scroll interactions.

## Features Maintained

All original functionality has been preserved:

- ✅ Three.js 3D scene with stars, nebula, mountains, and atmosphere
- ✅ Smooth camera movement and parallax effects
- ✅ GSAP animations for UI elements
- ✅ Scroll-based section transitions
- ✅ Responsive design
- ✅ Post-processing effects (bloom, tone mapping)
- ✅ Performance optimizations (throttled resize, RAF animations)

## Browser Compatibility

Same as the original React component:
- Modern browsers with WebGL support
- ES6+ JavaScript features
- CSS Grid and Flexbox support

## Performance Notes

The component includes the same performance optimizations as the original:
- Throttled resize handling
- RequestAnimationFrame for smooth animations
- Proper Three.js resource disposal on unmount
- Passive scroll event listeners
