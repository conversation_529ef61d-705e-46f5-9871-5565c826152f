{"hash": "e7cf6e48", "configHash": "cac937e1", "lockfileHash": "b198b4ef", "browserHash": "470fa4b3", "optimized": {"vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "813216b9", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "d383edfd", "needsInterop": false}, "three": {"src": "../../three/build/three.module.js", "file": "three.js", "fileHash": "c425761a", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "58f1c66d", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "a3dd4ce3", "needsInterop": false}, "three/addons/postprocessing/EffectComposer.js": {"src": "../../three/examples/jsm/postprocessing/EffectComposer.js", "file": "three_addons_postprocessing_EffectComposer__js.js", "fileHash": "8e268eab", "needsInterop": false}, "three/addons/postprocessing/RenderPass.js": {"src": "../../three/examples/jsm/postprocessing/RenderPass.js", "file": "three_addons_postprocessing_RenderPass__js.js", "fileHash": "43ac558b", "needsInterop": false}, "three/addons/postprocessing/UnrealBloomPass.js": {"src": "../../three/examples/jsm/postprocessing/UnrealBloomPass.js", "file": "three_addons_postprocessing_UnrealBloomPass__js.js", "fileHash": "223bce22", "needsInterop": false}}, "chunks": {"chunk-NY4Z4LNU": {"file": "chunk-NY4Z4LNU.js"}, "chunk-2S7NSVPO": {"file": "chunk-2S7NSVPO.js"}, "chunk-5MKLNLKQ": {"file": "chunk-5MKLNLKQ.js"}, "chunk-FIAHBV72": {"file": "chunk-FIAHBV72.js"}}}