{"version": 3, "sources": ["../../three/examples/jsm/shaders/LuminosityHighPassShader.js", "../../three/examples/jsm/postprocessing/UnrealBloomPass.js"], "sourcesContent": ["import {\n\tColor\n} from 'three';\n\n/**\n * @module LuminosityHighPassShader\n * @three_import import { LuminosityHighPassShader } from 'three/addons/shaders/LuminosityHighPassShader.js';\n */\n\n/**\n * Luminosity high pass shader.\n *\n * @constant\n * @type {ShaderMaterial~Shader}\n */\nconst LuminosityHighPassShader = {\n\n\tname: 'LuminosityHighPassShader',\n\n\tuniforms: {\n\n\t\t'tDiffuse': { value: null },\n\t\t'luminosityThreshold': { value: 1.0 },\n\t\t'smoothWidth': { value: 1.0 },\n\t\t'defaultColor': { value: new Color( 0x000000 ) },\n\t\t'defaultOpacity': { value: 0.0 }\n\n\t},\n\n\tvertexShader: /* glsl */`\n\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}`,\n\n\tfragmentShader: /* glsl */`\n\n\t\tuniform sampler2D tDiffuse;\n\t\tuniform vec3 defaultColor;\n\t\tuniform float defaultOpacity;\n\t\tuniform float luminosityThreshold;\n\t\tuniform float smoothWidth;\n\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvec4 texel = texture2D( tDiffuse, vUv );\n\n\t\t\tfloat v = luminance( texel.xyz );\n\n\t\t\tvec4 outputColor = vec4( defaultColor.rgb, defaultOpacity );\n\n\t\t\tfloat alpha = smoothstep( luminosityThreshold, luminosityThreshold + smoothWidth, v );\n\n\t\t\tgl_FragColor = mix( outputColor, texel, alpha );\n\n\t\t}`\n\n};\n\nexport { LuminosityHighPassShader };\n", "import {\n\tAdditiveBlending,\n\tColor,\n\tHalfFloatType,\n\tMeshBasicMaterial,\n\tShaderMaterial,\n\tUniformsUtils,\n\tVector2,\n\tVector3,\n\tWebGLRenderTarget\n} from 'three';\nimport { Pass, FullScreenQuad } from './Pass.js';\nimport { CopyShader } from '../shaders/CopyShader.js';\nimport { LuminosityHighPassShader } from '../shaders/LuminosityHighPassShader.js';\n\n/**\n * This pass is inspired by the bloom pass of Unreal Engine. It creates a\n * mip map chain of bloom textures and blurs them with different radii. Because\n * of the weighted combination of mips, and because larger blurs are done on\n * higher mips, this effect provides good quality and performance.\n *\n * When using this pass, tone mapping must be enabled in the renderer settings.\n *\n * Reference:\n * - [Bloom in Unreal Engine]{@link https://docs.unrealengine.com/latest/INT/Engine/Rendering/PostProcessEffects/Bloom/}\n *\n * ```js\n * const resolution = new THREE.Vector2( window.innerWidth, window.innerHeight );\n * const bloomPass = new UnrealBloomPass( resolution, 1.5, 0.4, 0.85 );\n * composer.addPass( bloomPass );\n * ```\n *\n * @augments Pass\n * @three_import import { UnrealBloomPass } from 'three/addons/postprocessing/UnrealBloomPass.js';\n */\nclass UnrealBloomPass extends Pass {\n\n\t/**\n\t * Constructs a new Unreal Bloom pass.\n\t *\n\t * @param {Vector2} [resolution] - The effect's resolution.\n\t * @param {number} [strength=1] - The Bloom strength.\n\t * @param {number} radius - The Bloom radius.\n\t * @param {number} threshold - The luminance threshold limits which bright areas contribute to the Bloom effect.\n\t */\n\tconstructor( resolution, strength = 1, radius, threshold ) {\n\n\t\tsuper();\n\n\t\t/**\n\t\t * The Bloom strength.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 1\n\t\t */\n\t\tthis.strength = strength;\n\n\t\t/**\n\t\t * The Bloom radius.\n\t\t *\n\t\t * @type {number}\n\t\t */\n\t\tthis.radius = radius;\n\n\t\t/**\n\t\t * The luminance threshold limits which bright areas contribute to the Bloom effect.\n\t\t *\n\t\t * @type {number}\n\t\t */\n\t\tthis.threshold = threshold;\n\n\t\t/**\n\t\t * The effect's resolution.\n\t\t *\n\t\t * @type {Vector2}\n\t\t * @default (256,256)\n\t\t */\n\t\tthis.resolution = ( resolution !== undefined ) ? new Vector2( resolution.x, resolution.y ) : new Vector2( 256, 256 );\n\n\t\t/**\n\t\t * The effect's clear color\n\t\t *\n\t\t * @type {Color}\n\t\t * @default (0,0,0)\n\t\t */\n\t\tthis.clearColor = new Color( 0, 0, 0 );\n\n\t\t/**\n\t\t * Overwritten to disable the swap.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.needsSwap = false;\n\n\t\t// internals\n\n\t\t// render targets\n\t\tthis.renderTargetsHorizontal = [];\n\t\tthis.renderTargetsVertical = [];\n\t\tthis.nMips = 5;\n\t\tlet resx = Math.round( this.resolution.x / 2 );\n\t\tlet resy = Math.round( this.resolution.y / 2 );\n\n\t\tthis.renderTargetBright = new WebGLRenderTarget( resx, resy, { type: HalfFloatType } );\n\t\tthis.renderTargetBright.texture.name = 'UnrealBloomPass.bright';\n\t\tthis.renderTargetBright.texture.generateMipmaps = false;\n\n\t\tfor ( let i = 0; i < this.nMips; i ++ ) {\n\n\t\t\tconst renderTargetHorizontal = new WebGLRenderTarget( resx, resy, { type: HalfFloatType } );\n\n\t\t\trenderTargetHorizontal.texture.name = 'UnrealBloomPass.h' + i;\n\t\t\trenderTargetHorizontal.texture.generateMipmaps = false;\n\n\t\t\tthis.renderTargetsHorizontal.push( renderTargetHorizontal );\n\n\t\t\tconst renderTargetVertical = new WebGLRenderTarget( resx, resy, { type: HalfFloatType } );\n\n\t\t\trenderTargetVertical.texture.name = 'UnrealBloomPass.v' + i;\n\t\t\trenderTargetVertical.texture.generateMipmaps = false;\n\n\t\t\tthis.renderTargetsVertical.push( renderTargetVertical );\n\n\t\t\tresx = Math.round( resx / 2 );\n\n\t\t\tresy = Math.round( resy / 2 );\n\n\t\t}\n\n\t\t// luminosity high pass material\n\n\t\tconst highPassShader = LuminosityHighPassShader;\n\t\tthis.highPassUniforms = UniformsUtils.clone( highPassShader.uniforms );\n\n\t\tthis.highPassUniforms[ 'luminosityThreshold' ].value = threshold;\n\t\tthis.highPassUniforms[ 'smoothWidth' ].value = 0.01;\n\n\t\tthis.materialHighPassFilter = new ShaderMaterial( {\n\t\t\tuniforms: this.highPassUniforms,\n\t\t\tvertexShader: highPassShader.vertexShader,\n\t\t\tfragmentShader: highPassShader.fragmentShader\n\t\t} );\n\n\t\t// gaussian blur materials\n\n\t\tthis.separableBlurMaterials = [];\n\t\tconst kernelSizeArray = [ 3, 5, 7, 9, 11 ];\n\t\tresx = Math.round( this.resolution.x / 2 );\n\t\tresy = Math.round( this.resolution.y / 2 );\n\n\t\tfor ( let i = 0; i < this.nMips; i ++ ) {\n\n\t\t\tthis.separableBlurMaterials.push( this._getSeparableBlurMaterial( kernelSizeArray[ i ] ) );\n\n\t\t\tthis.separableBlurMaterials[ i ].uniforms[ 'invSize' ].value = new Vector2( 1 / resx, 1 / resy );\n\n\t\t\tresx = Math.round( resx / 2 );\n\n\t\t\tresy = Math.round( resy / 2 );\n\n\t\t}\n\n\t\t// composite material\n\n\t\tthis.compositeMaterial = this._getCompositeMaterial( this.nMips );\n\t\tthis.compositeMaterial.uniforms[ 'blurTexture1' ].value = this.renderTargetsVertical[ 0 ].texture;\n\t\tthis.compositeMaterial.uniforms[ 'blurTexture2' ].value = this.renderTargetsVertical[ 1 ].texture;\n\t\tthis.compositeMaterial.uniforms[ 'blurTexture3' ].value = this.renderTargetsVertical[ 2 ].texture;\n\t\tthis.compositeMaterial.uniforms[ 'blurTexture4' ].value = this.renderTargetsVertical[ 3 ].texture;\n\t\tthis.compositeMaterial.uniforms[ 'blurTexture5' ].value = this.renderTargetsVertical[ 4 ].texture;\n\t\tthis.compositeMaterial.uniforms[ 'bloomStrength' ].value = strength;\n\t\tthis.compositeMaterial.uniforms[ 'bloomRadius' ].value = 0.1;\n\n\t\tconst bloomFactors = [ 1.0, 0.8, 0.6, 0.4, 0.2 ];\n\t\tthis.compositeMaterial.uniforms[ 'bloomFactors' ].value = bloomFactors;\n\t\tthis.bloomTintColors = [ new Vector3( 1, 1, 1 ), new Vector3( 1, 1, 1 ), new Vector3( 1, 1, 1 ), new Vector3( 1, 1, 1 ), new Vector3( 1, 1, 1 ) ];\n\t\tthis.compositeMaterial.uniforms[ 'bloomTintColors' ].value = this.bloomTintColors;\n\n\t\t// blend material\n\n\t\tthis.copyUniforms = UniformsUtils.clone( CopyShader.uniforms );\n\n\t\tthis.blendMaterial = new ShaderMaterial( {\n\t\t\tuniforms: this.copyUniforms,\n\t\t\tvertexShader: CopyShader.vertexShader,\n\t\t\tfragmentShader: CopyShader.fragmentShader,\n\t\t\tblending: AdditiveBlending,\n\t\t\tdepthTest: false,\n\t\t\tdepthWrite: false,\n\t\t\ttransparent: true\n\t\t} );\n\n\t\tthis._oldClearColor = new Color();\n\t\tthis._oldClearAlpha = 1;\n\n\t\tthis._basic = new MeshBasicMaterial();\n\n\t\tthis._fsQuad = new FullScreenQuad( null );\n\n\t}\n\n\t/**\n\t * Frees the GPU-related resources allocated by this instance. Call this\n\t * method whenever the pass is no longer used in your app.\n\t */\n\tdispose() {\n\n\t\tfor ( let i = 0; i < this.renderTargetsHorizontal.length; i ++ ) {\n\n\t\t\tthis.renderTargetsHorizontal[ i ].dispose();\n\n\t\t}\n\n\t\tfor ( let i = 0; i < this.renderTargetsVertical.length; i ++ ) {\n\n\t\t\tthis.renderTargetsVertical[ i ].dispose();\n\n\t\t}\n\n\t\tthis.renderTargetBright.dispose();\n\n\t\t//\n\n\t\tfor ( let i = 0; i < this.separableBlurMaterials.length; i ++ ) {\n\n\t\t\tthis.separableBlurMaterials[ i ].dispose();\n\n\t\t}\n\n\t\tthis.compositeMaterial.dispose();\n\t\tthis.blendMaterial.dispose();\n\t\tthis._basic.dispose();\n\n\t\t//\n\n\t\tthis._fsQuad.dispose();\n\n\t}\n\n\t/**\n\t * Sets the size of the pass.\n\t *\n\t * @param {number} width - The width to set.\n\t * @param {number} height - The height to set.\n\t */\n\tsetSize( width, height ) {\n\n\t\tlet resx = Math.round( width / 2 );\n\t\tlet resy = Math.round( height / 2 );\n\n\t\tthis.renderTargetBright.setSize( resx, resy );\n\n\t\tfor ( let i = 0; i < this.nMips; i ++ ) {\n\n\t\t\tthis.renderTargetsHorizontal[ i ].setSize( resx, resy );\n\t\t\tthis.renderTargetsVertical[ i ].setSize( resx, resy );\n\n\t\t\tthis.separableBlurMaterials[ i ].uniforms[ 'invSize' ].value = new Vector2( 1 / resx, 1 / resy );\n\n\t\t\tresx = Math.round( resx / 2 );\n\t\t\tresy = Math.round( resy / 2 );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Performs the Bloom pass.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} writeBuffer - The write buffer. This buffer is intended as the rendering\n\t * destination for the pass.\n\t * @param {WebGLRenderTarget} readBuffer - The read buffer. The pass can access the result from the\n\t * previous pass from this buffer.\n\t * @param {number} deltaTime - The delta time in seconds.\n\t * @param {boolean} maskActive - Whether masking is active or not.\n\t */\n\trender( renderer, writeBuffer, readBuffer, deltaTime, maskActive ) {\n\n\t\trenderer.getClearColor( this._oldClearColor );\n\t\tthis._oldClearAlpha = renderer.getClearAlpha();\n\t\tconst oldAutoClear = renderer.autoClear;\n\t\trenderer.autoClear = false;\n\n\t\trenderer.setClearColor( this.clearColor, 0 );\n\n\t\tif ( maskActive ) renderer.state.buffers.stencil.setTest( false );\n\n\t\t// Render input to screen\n\n\t\tif ( this.renderToScreen ) {\n\n\t\t\tthis._fsQuad.material = this._basic;\n\t\t\tthis._basic.map = readBuffer.texture;\n\n\t\t\trenderer.setRenderTarget( null );\n\t\t\trenderer.clear();\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t}\n\n\t\t// 1. Extract Bright Areas\n\n\t\tthis.highPassUniforms[ 'tDiffuse' ].value = readBuffer.texture;\n\t\tthis.highPassUniforms[ 'luminosityThreshold' ].value = this.threshold;\n\t\tthis._fsQuad.material = this.materialHighPassFilter;\n\n\t\trenderer.setRenderTarget( this.renderTargetBright );\n\t\trenderer.clear();\n\t\tthis._fsQuad.render( renderer );\n\n\t\t// 2. Blur All the mips progressively\n\n\t\tlet inputRenderTarget = this.renderTargetBright;\n\n\t\tfor ( let i = 0; i < this.nMips; i ++ ) {\n\n\t\t\tthis._fsQuad.material = this.separableBlurMaterials[ i ];\n\n\t\t\tthis.separableBlurMaterials[ i ].uniforms[ 'colorTexture' ].value = inputRenderTarget.texture;\n\t\t\tthis.separableBlurMaterials[ i ].uniforms[ 'direction' ].value = UnrealBloomPass.BlurDirectionX;\n\t\t\trenderer.setRenderTarget( this.renderTargetsHorizontal[ i ] );\n\t\t\trenderer.clear();\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t\tthis.separableBlurMaterials[ i ].uniforms[ 'colorTexture' ].value = this.renderTargetsHorizontal[ i ].texture;\n\t\t\tthis.separableBlurMaterials[ i ].uniforms[ 'direction' ].value = UnrealBloomPass.BlurDirectionY;\n\t\t\trenderer.setRenderTarget( this.renderTargetsVertical[ i ] );\n\t\t\trenderer.clear();\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t\tinputRenderTarget = this.renderTargetsVertical[ i ];\n\n\t\t}\n\n\t\t// Composite All the mips\n\n\t\tthis._fsQuad.material = this.compositeMaterial;\n\t\tthis.compositeMaterial.uniforms[ 'bloomStrength' ].value = this.strength;\n\t\tthis.compositeMaterial.uniforms[ 'bloomRadius' ].value = this.radius;\n\t\tthis.compositeMaterial.uniforms[ 'bloomTintColors' ].value = this.bloomTintColors;\n\n\t\trenderer.setRenderTarget( this.renderTargetsHorizontal[ 0 ] );\n\t\trenderer.clear();\n\t\tthis._fsQuad.render( renderer );\n\n\t\t// Blend it additively over the input texture\n\n\t\tthis._fsQuad.material = this.blendMaterial;\n\t\tthis.copyUniforms[ 'tDiffuse' ].value = this.renderTargetsHorizontal[ 0 ].texture;\n\n\t\tif ( maskActive ) renderer.state.buffers.stencil.setTest( true );\n\n\t\tif ( this.renderToScreen ) {\n\n\t\t\trenderer.setRenderTarget( null );\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t} else {\n\n\t\t\trenderer.setRenderTarget( readBuffer );\n\t\t\tthis._fsQuad.render( renderer );\n\n\t\t}\n\n\t\t// Restore renderer settings\n\n\t\trenderer.setClearColor( this._oldClearColor, this._oldClearAlpha );\n\t\trenderer.autoClear = oldAutoClear;\n\n\t}\n\n\t// internals\n\n\t_getSeparableBlurMaterial( kernelRadius ) {\n\n\t\tconst coefficients = [];\n\n\t\tfor ( let i = 0; i < kernelRadius; i ++ ) {\n\n\t\t\tcoefficients.push( 0.39894 * Math.exp( - 0.5 * i * i / ( kernelRadius * kernelRadius ) ) / kernelRadius );\n\n\t\t}\n\n\t\treturn new ShaderMaterial( {\n\n\t\t\tdefines: {\n\t\t\t\t'KERNEL_RADIUS': kernelRadius\n\t\t\t},\n\n\t\t\tuniforms: {\n\t\t\t\t'colorTexture': { value: null },\n\t\t\t\t'invSize': { value: new Vector2( 0.5, 0.5 ) }, // inverse texture size\n\t\t\t\t'direction': { value: new Vector2( 0.5, 0.5 ) },\n\t\t\t\t'gaussianCoefficients': { value: coefficients } // precomputed Gaussian coefficients\n\t\t\t},\n\n\t\t\tvertexShader:\n\t\t\t\t`varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n\t\t\tfragmentShader:\n\t\t\t\t`#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 invSize;\n\t\t\t\tuniform vec2 direction;\n\t\t\t\tuniform float gaussianCoefficients[KERNEL_RADIUS];\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tfloat weightSum = gaussianCoefficients[0];\n\t\t\t\t\tvec3 diffuseSum = texture2D( colorTexture, vUv ).rgb * weightSum;\n\t\t\t\t\tfor( int i = 1; i < KERNEL_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat x = float(i);\n\t\t\t\t\t\tfloat w = gaussianCoefficients[i];\n\t\t\t\t\t\tvec2 uvOffset = direction * invSize * x;\n\t\t\t\t\t\tvec3 sample1 = texture2D( colorTexture, vUv + uvOffset ).rgb;\n\t\t\t\t\t\tvec3 sample2 = texture2D( colorTexture, vUv - uvOffset ).rgb;\n\t\t\t\t\t\tdiffuseSum += (sample1 + sample2) * w;\n\t\t\t\t\t\tweightSum += 2.0 * w;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = vec4(diffuseSum/weightSum, 1.0);\n\t\t\t\t}`\n\t\t} );\n\n\t}\n\n\t_getCompositeMaterial( nMips ) {\n\n\t\treturn new ShaderMaterial( {\n\n\t\t\tdefines: {\n\t\t\t\t'NUM_MIPS': nMips\n\t\t\t},\n\n\t\t\tuniforms: {\n\t\t\t\t'blurTexture1': { value: null },\n\t\t\t\t'blurTexture2': { value: null },\n\t\t\t\t'blurTexture3': { value: null },\n\t\t\t\t'blurTexture4': { value: null },\n\t\t\t\t'blurTexture5': { value: null },\n\t\t\t\t'bloomStrength': { value: 1.0 },\n\t\t\t\t'bloomFactors': { value: null },\n\t\t\t\t'bloomTintColors': { value: null },\n\t\t\t\t'bloomRadius': { value: 0.0 }\n\t\t\t},\n\n\t\t\tvertexShader:\n\t\t\t\t`varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n\t\t\tfragmentShader:\n\t\t\t\t`varying vec2 vUv;\n\t\t\t\tuniform sampler2D blurTexture1;\n\t\t\t\tuniform sampler2D blurTexture2;\n\t\t\t\tuniform sampler2D blurTexture3;\n\t\t\t\tuniform sampler2D blurTexture4;\n\t\t\t\tuniform sampler2D blurTexture5;\n\t\t\t\tuniform float bloomStrength;\n\t\t\t\tuniform float bloomRadius;\n\t\t\t\tuniform float bloomFactors[NUM_MIPS];\n\t\t\t\tuniform vec3 bloomTintColors[NUM_MIPS];\n\n\t\t\t\tfloat lerpBloomFactor(const in float factor) {\n\t\t\t\t\tfloat mirrorFactor = 1.2 - factor;\n\t\t\t\t\treturn mix(factor, mirrorFactor, bloomRadius);\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tgl_FragColor = bloomStrength * ( lerpBloomFactor(bloomFactors[0]) * vec4(bloomTintColors[0], 1.0) * texture2D(blurTexture1, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[1]) * vec4(bloomTintColors[1], 1.0) * texture2D(blurTexture2, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[2]) * vec4(bloomTintColors[2], 1.0) * texture2D(blurTexture3, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[3]) * vec4(bloomTintColors[3], 1.0) * texture2D(blurTexture4, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[4]) * vec4(bloomTintColors[4], 1.0) * texture2D(blurTexture5, vUv) );\n\t\t\t\t}`\n\t\t} );\n\n\t}\n\n}\n\nUnrealBloomPass.BlurDirectionX = new Vector2( 1.0, 0.0 );\nUnrealBloomPass.BlurDirectionY = new Vector2( 0.0, 1.0 );\n\nexport { UnrealBloomPass };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAeA,IAAM,2BAA2B;AAAA,EAEhC,MAAM;AAAA,EAEN,UAAU;AAAA,IAET,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,uBAAuB,EAAE,OAAO,EAAI;AAAA,IACpC,eAAe,EAAE,OAAO,EAAI;AAAA,IAC5B,gBAAgB,EAAE,OAAO,IAAI,MAAO,CAAS,EAAE;AAAA,IAC/C,kBAAkB,EAAE,OAAO,EAAI;AAAA,EAEhC;AAAA,EAEA;AAAA;AAAA,IAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYxB;AAAA;AAAA,IAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwB3B;;;AC9BA,IAAM,kBAAN,MAAM,yBAAwB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlC,YAAa,YAAY,WAAW,GAAG,QAAQ,WAAY;AAE1D,UAAM;AAQN,SAAK,WAAW;AAOhB,SAAK,SAAS;AAOd,SAAK,YAAY;AAQjB,SAAK,aAAe,eAAe,SAAc,IAAI,QAAS,WAAW,GAAG,WAAW,CAAE,IAAI,IAAI,QAAS,KAAK,GAAI;AAQnH,SAAK,aAAa,IAAI,MAAO,GAAG,GAAG,CAAE;AAQrC,SAAK,YAAY;AAKjB,SAAK,0BAA0B,CAAC;AAChC,SAAK,wBAAwB,CAAC;AAC9B,SAAK,QAAQ;AACb,QAAI,OAAO,KAAK,MAAO,KAAK,WAAW,IAAI,CAAE;AAC7C,QAAI,OAAO,KAAK,MAAO,KAAK,WAAW,IAAI,CAAE;AAE7C,SAAK,qBAAqB,IAAI,kBAAmB,MAAM,MAAM,EAAE,MAAM,cAAc,CAAE;AACrF,SAAK,mBAAmB,QAAQ,OAAO;AACvC,SAAK,mBAAmB,QAAQ,kBAAkB;AAElD,aAAU,IAAI,GAAG,IAAI,KAAK,OAAO,KAAO;AAEvC,YAAM,yBAAyB,IAAI,kBAAmB,MAAM,MAAM,EAAE,MAAM,cAAc,CAAE;AAE1F,6BAAuB,QAAQ,OAAO,sBAAsB;AAC5D,6BAAuB,QAAQ,kBAAkB;AAEjD,WAAK,wBAAwB,KAAM,sBAAuB;AAE1D,YAAM,uBAAuB,IAAI,kBAAmB,MAAM,MAAM,EAAE,MAAM,cAAc,CAAE;AAExF,2BAAqB,QAAQ,OAAO,sBAAsB;AAC1D,2BAAqB,QAAQ,kBAAkB;AAE/C,WAAK,sBAAsB,KAAM,oBAAqB;AAEtD,aAAO,KAAK,MAAO,OAAO,CAAE;AAE5B,aAAO,KAAK,MAAO,OAAO,CAAE;AAAA,IAE7B;AAIA,UAAM,iBAAiB;AACvB,SAAK,mBAAmB,cAAc,MAAO,eAAe,QAAS;AAErE,SAAK,iBAAkB,qBAAsB,EAAE,QAAQ;AACvD,SAAK,iBAAkB,aAAc,EAAE,QAAQ;AAE/C,SAAK,yBAAyB,IAAI,eAAgB;AAAA,MACjD,UAAU,KAAK;AAAA,MACf,cAAc,eAAe;AAAA,MAC7B,gBAAgB,eAAe;AAAA,IAChC,CAAE;AAIF,SAAK,yBAAyB,CAAC;AAC/B,UAAM,kBAAkB,CAAE,GAAG,GAAG,GAAG,GAAG,EAAG;AACzC,WAAO,KAAK,MAAO,KAAK,WAAW,IAAI,CAAE;AACzC,WAAO,KAAK,MAAO,KAAK,WAAW,IAAI,CAAE;AAEzC,aAAU,IAAI,GAAG,IAAI,KAAK,OAAO,KAAO;AAEvC,WAAK,uBAAuB,KAAM,KAAK,0BAA2B,gBAAiB,CAAE,CAAE,CAAE;AAEzF,WAAK,uBAAwB,CAAE,EAAE,SAAU,SAAU,EAAE,QAAQ,IAAI,QAAS,IAAI,MAAM,IAAI,IAAK;AAE/F,aAAO,KAAK,MAAO,OAAO,CAAE;AAE5B,aAAO,KAAK,MAAO,OAAO,CAAE;AAAA,IAE7B;AAIA,SAAK,oBAAoB,KAAK,sBAAuB,KAAK,KAAM;AAChE,SAAK,kBAAkB,SAAU,cAAe,EAAE,QAAQ,KAAK,sBAAuB,CAAE,EAAE;AAC1F,SAAK,kBAAkB,SAAU,cAAe,EAAE,QAAQ,KAAK,sBAAuB,CAAE,EAAE;AAC1F,SAAK,kBAAkB,SAAU,cAAe,EAAE,QAAQ,KAAK,sBAAuB,CAAE,EAAE;AAC1F,SAAK,kBAAkB,SAAU,cAAe,EAAE,QAAQ,KAAK,sBAAuB,CAAE,EAAE;AAC1F,SAAK,kBAAkB,SAAU,cAAe,EAAE,QAAQ,KAAK,sBAAuB,CAAE,EAAE;AAC1F,SAAK,kBAAkB,SAAU,eAAgB,EAAE,QAAQ;AAC3D,SAAK,kBAAkB,SAAU,aAAc,EAAE,QAAQ;AAEzD,UAAM,eAAe,CAAE,GAAK,KAAK,KAAK,KAAK,GAAI;AAC/C,SAAK,kBAAkB,SAAU,cAAe,EAAE,QAAQ;AAC1D,SAAK,kBAAkB,CAAE,IAAI,QAAS,GAAG,GAAG,CAAE,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE,CAAE;AAChJ,SAAK,kBAAkB,SAAU,iBAAkB,EAAE,QAAQ,KAAK;AAIlE,SAAK,eAAe,cAAc,MAAO,WAAW,QAAS;AAE7D,SAAK,gBAAgB,IAAI,eAAgB;AAAA,MACxC,UAAU,KAAK;AAAA,MACf,cAAc,WAAW;AAAA,MACzB,gBAAgB,WAAW;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACd,CAAE;AAEF,SAAK,iBAAiB,IAAI,MAAM;AAChC,SAAK,iBAAiB;AAEtB,SAAK,SAAS,IAAI,kBAAkB;AAEpC,SAAK,UAAU,IAAI,eAAgB,IAAK;AAAA,EAEzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AAET,aAAU,IAAI,GAAG,IAAI,KAAK,wBAAwB,QAAQ,KAAO;AAEhE,WAAK,wBAAyB,CAAE,EAAE,QAAQ;AAAA,IAE3C;AAEA,aAAU,IAAI,GAAG,IAAI,KAAK,sBAAsB,QAAQ,KAAO;AAE9D,WAAK,sBAAuB,CAAE,EAAE,QAAQ;AAAA,IAEzC;AAEA,SAAK,mBAAmB,QAAQ;AAIhC,aAAU,IAAI,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAO;AAE/D,WAAK,uBAAwB,CAAE,EAAE,QAAQ;AAAA,IAE1C;AAEA,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,cAAc,QAAQ;AAC3B,SAAK,OAAO,QAAQ;AAIpB,SAAK,QAAQ,QAAQ;AAAA,EAEtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAS,OAAO,QAAS;AAExB,QAAI,OAAO,KAAK,MAAO,QAAQ,CAAE;AACjC,QAAI,OAAO,KAAK,MAAO,SAAS,CAAE;AAElC,SAAK,mBAAmB,QAAS,MAAM,IAAK;AAE5C,aAAU,IAAI,GAAG,IAAI,KAAK,OAAO,KAAO;AAEvC,WAAK,wBAAyB,CAAE,EAAE,QAAS,MAAM,IAAK;AACtD,WAAK,sBAAuB,CAAE,EAAE,QAAS,MAAM,IAAK;AAEpD,WAAK,uBAAwB,CAAE,EAAE,SAAU,SAAU,EAAE,QAAQ,IAAI,QAAS,IAAI,MAAM,IAAI,IAAK;AAE/F,aAAO,KAAK,MAAO,OAAO,CAAE;AAC5B,aAAO,KAAK,MAAO,OAAO,CAAE;AAAA,IAE7B;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAQ,UAAU,aAAa,YAAY,WAAW,YAAa;AAElE,aAAS,cAAe,KAAK,cAAe;AAC5C,SAAK,iBAAiB,SAAS,cAAc;AAC7C,UAAM,eAAe,SAAS;AAC9B,aAAS,YAAY;AAErB,aAAS,cAAe,KAAK,YAAY,CAAE;AAE3C,QAAK,WAAa,UAAS,MAAM,QAAQ,QAAQ,QAAS,KAAM;AAIhE,QAAK,KAAK,gBAAiB;AAE1B,WAAK,QAAQ,WAAW,KAAK;AAC7B,WAAK,OAAO,MAAM,WAAW;AAE7B,eAAS,gBAAiB,IAAK;AAC/B,eAAS,MAAM;AACf,WAAK,QAAQ,OAAQ,QAAS;AAAA,IAE/B;AAIA,SAAK,iBAAkB,UAAW,EAAE,QAAQ,WAAW;AACvD,SAAK,iBAAkB,qBAAsB,EAAE,QAAQ,KAAK;AAC5D,SAAK,QAAQ,WAAW,KAAK;AAE7B,aAAS,gBAAiB,KAAK,kBAAmB;AAClD,aAAS,MAAM;AACf,SAAK,QAAQ,OAAQ,QAAS;AAI9B,QAAI,oBAAoB,KAAK;AAE7B,aAAU,IAAI,GAAG,IAAI,KAAK,OAAO,KAAO;AAEvC,WAAK,QAAQ,WAAW,KAAK,uBAAwB,CAAE;AAEvD,WAAK,uBAAwB,CAAE,EAAE,SAAU,cAAe,EAAE,QAAQ,kBAAkB;AACtF,WAAK,uBAAwB,CAAE,EAAE,SAAU,WAAY,EAAE,QAAQ,iBAAgB;AACjF,eAAS,gBAAiB,KAAK,wBAAyB,CAAE,CAAE;AAC5D,eAAS,MAAM;AACf,WAAK,QAAQ,OAAQ,QAAS;AAE9B,WAAK,uBAAwB,CAAE,EAAE,SAAU,cAAe,EAAE,QAAQ,KAAK,wBAAyB,CAAE,EAAE;AACtG,WAAK,uBAAwB,CAAE,EAAE,SAAU,WAAY,EAAE,QAAQ,iBAAgB;AACjF,eAAS,gBAAiB,KAAK,sBAAuB,CAAE,CAAE;AAC1D,eAAS,MAAM;AACf,WAAK,QAAQ,OAAQ,QAAS;AAE9B,0BAAoB,KAAK,sBAAuB,CAAE;AAAA,IAEnD;AAIA,SAAK,QAAQ,WAAW,KAAK;AAC7B,SAAK,kBAAkB,SAAU,eAAgB,EAAE,QAAQ,KAAK;AAChE,SAAK,kBAAkB,SAAU,aAAc,EAAE,QAAQ,KAAK;AAC9D,SAAK,kBAAkB,SAAU,iBAAkB,EAAE,QAAQ,KAAK;AAElE,aAAS,gBAAiB,KAAK,wBAAyB,CAAE,CAAE;AAC5D,aAAS,MAAM;AACf,SAAK,QAAQ,OAAQ,QAAS;AAI9B,SAAK,QAAQ,WAAW,KAAK;AAC7B,SAAK,aAAc,UAAW,EAAE,QAAQ,KAAK,wBAAyB,CAAE,EAAE;AAE1E,QAAK,WAAa,UAAS,MAAM,QAAQ,QAAQ,QAAS,IAAK;AAE/D,QAAK,KAAK,gBAAiB;AAE1B,eAAS,gBAAiB,IAAK;AAC/B,WAAK,QAAQ,OAAQ,QAAS;AAAA,IAE/B,OAAO;AAEN,eAAS,gBAAiB,UAAW;AACrC,WAAK,QAAQ,OAAQ,QAAS;AAAA,IAE/B;AAIA,aAAS,cAAe,KAAK,gBAAgB,KAAK,cAAe;AACjE,aAAS,YAAY;AAAA,EAEtB;AAAA;AAAA,EAIA,0BAA2B,cAAe;AAEzC,UAAM,eAAe,CAAC;AAEtB,aAAU,IAAI,GAAG,IAAI,cAAc,KAAO;AAEzC,mBAAa,KAAM,UAAU,KAAK,IAAK,OAAQ,IAAI,KAAM,eAAe,aAAe,IAAI,YAAa;AAAA,IAEzG;AAEA,WAAO,IAAI,eAAgB;AAAA,MAE1B,SAAS;AAAA,QACR,iBAAiB;AAAA,MAClB;AAAA,MAEA,UAAU;AAAA,QACT,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,WAAW,EAAE,OAAO,IAAI,QAAS,KAAK,GAAI,EAAE;AAAA;AAAA,QAC5C,aAAa,EAAE,OAAO,IAAI,QAAS,KAAK,GAAI,EAAE;AAAA,QAC9C,wBAAwB,EAAE,OAAO,aAAa;AAAA;AAAA,MAC/C;AAAA,MAEA,cACC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMD,gBACC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAqBF,CAAE;AAAA,EAEH;AAAA,EAEA,sBAAuB,OAAQ;AAE9B,WAAO,IAAI,eAAgB;AAAA,MAE1B,SAAS;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MAEA,UAAU;AAAA,QACT,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,iBAAiB,EAAE,OAAO,EAAI;AAAA,QAC9B,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,mBAAmB,EAAE,OAAO,KAAK;AAAA,QACjC,eAAe,EAAE,OAAO,EAAI;AAAA,MAC7B;AAAA,MAEA,cACC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMD,gBACC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuBF,CAAE;AAAA,EAEH;AAED;AAEA,gBAAgB,iBAAiB,IAAI,QAAS,GAAK,CAAI;AACvD,gBAAgB,iBAAiB,IAAI,QAAS,GAAK,CAAI;", "names": []}