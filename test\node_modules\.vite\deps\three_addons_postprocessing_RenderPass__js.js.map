{"version": 3, "sources": ["../../three/examples/jsm/postprocessing/RenderPass.js"], "sourcesContent": ["import {\n\tColor\n} from 'three';\nimport { Pass } from './Pass.js';\n\n/**\n * This class represents a render pass. It takes a camera and a scene and produces\n * a beauty pass for subsequent post processing effects.\n *\n * ```js\n * const renderPass = new RenderPass( scene, camera );\n * composer.addPass( renderPass );\n * ```\n *\n * @augments Pass\n * @three_import import { RenderPass } from 'three/addons/postprocessing/RenderPass.js';\n */\nclass RenderPass extends Pass {\n\n\t/**\n\t * Constructs a new render pass.\n\t *\n\t * @param {Scene} scene - The scene to render.\n\t * @param {Camera} camera - The camera.\n\t * @param {?Material} [overrideMaterial=null] - The override material. If set, this material is used\n\t * for all objects in the scene.\n\t * @param {?(number|Color|string)} [clearColor=null] - The clear color of the render pass.\n\t * @param {?number} [clearAlpha=null] - The clear alpha of the render pass.\n\t */\n\tconstructor( scene, camera, overrideMaterial = null, clearColor = null, clearAlpha = null ) {\n\n\t\tsuper();\n\n\t\t/**\n\t\t * The scene to render.\n\t\t *\n\t\t * @type {Scene}\n\t\t */\n\t\tthis.scene = scene;\n\n\t\t/**\n\t\t * The camera.\n\t\t *\n\t\t * @type {Camera}\n\t\t */\n\t\tthis.camera = camera;\n\n\t\t/**\n\t\t * The override material. If set, this material is used\n\t\t * for all objects in the scene.\n\t\t *\n\t\t * @type {?Material}\n\t\t * @default null\n\t\t */\n\t\tthis.overrideMaterial = overrideMaterial;\n\n\t\t/**\n\t\t * The clear color of the render pass.\n\t\t *\n\t\t * @type {?(number|Color|string)}\n\t\t * @default null\n\t\t */\n\t\tthis.clearColor = clearColor;\n\n\t\t/**\n\t\t * The clear alpha of the render pass.\n\t\t *\n\t\t * @type {?number}\n\t\t * @default null\n\t\t */\n\t\tthis.clearAlpha = clearAlpha;\n\n\t\t/**\n\t\t * Overwritten to perform a clear operation by default.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.clear = true;\n\n\t\t/**\n\t\t * If set to `true`, only the depth can be cleared when `clear` is to `false`.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.clearDepth = false;\n\n\t\t/**\n\t\t * Overwritten to disable the swap.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.needsSwap = false;\n\t\tthis._oldClearColor = new Color();\n\n\t}\n\n\t/**\n\t * Performs a beauty pass with the configured scene and camera.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} writeBuffer - The write buffer. This buffer is intended as the rendering\n\t * destination for the pass.\n\t * @param {WebGLRenderTarget} readBuffer - The read buffer. The pass can access the result from the\n\t * previous pass from this buffer.\n\t * @param {number} deltaTime - The delta time in seconds.\n\t * @param {boolean} maskActive - Whether masking is active or not.\n\t */\n\trender( renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */ ) {\n\n\t\tconst oldAutoClear = renderer.autoClear;\n\t\trenderer.autoClear = false;\n\n\t\tlet oldClearAlpha, oldOverrideMaterial;\n\n\t\tif ( this.overrideMaterial !== null ) {\n\n\t\t\toldOverrideMaterial = this.scene.overrideMaterial;\n\n\t\t\tthis.scene.overrideMaterial = this.overrideMaterial;\n\n\t\t}\n\n\t\tif ( this.clearColor !== null ) {\n\n\t\t\trenderer.getClearColor( this._oldClearColor );\n\t\t\trenderer.setClearColor( this.clearColor, renderer.getClearAlpha() );\n\n\t\t}\n\n\t\tif ( this.clearAlpha !== null ) {\n\n\t\t\toldClearAlpha = renderer.getClearAlpha();\n\t\t\trenderer.setClearAlpha( this.clearAlpha );\n\n\t\t}\n\n\t\tif ( this.clearDepth == true ) {\n\n\t\t\trenderer.clearDepth();\n\n\t\t}\n\n\t\trenderer.setRenderTarget( this.renderToScreen ? null : readBuffer );\n\n\t\tif ( this.clear === true ) {\n\n\t\t\t// TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n\t\t\trenderer.clear( renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil );\n\n\t\t}\n\n\t\trenderer.render( this.scene, this.camera );\n\n\t\t// restore\n\n\t\tif ( this.clearColor !== null ) {\n\n\t\t\trenderer.setClearColor( this._oldClearColor );\n\n\t\t}\n\n\t\tif ( this.clearAlpha !== null ) {\n\n\t\t\trenderer.setClearAlpha( oldClearAlpha );\n\n\t\t}\n\n\t\tif ( this.overrideMaterial !== null ) {\n\n\t\t\tthis.scene.overrideMaterial = oldOverrideMaterial;\n\n\t\t}\n\n\t\trenderer.autoClear = oldAutoClear;\n\n\t}\n\n}\n\nexport { RenderPass };\n"], "mappings": ";;;;;;;;AAiBA,IAAM,aAAN,cAAyB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY7B,YAAa,OAAO,QAAQ,mBAAmB,MAAM,aAAa,MAAM,aAAa,MAAO;AAE3F,UAAM;AAON,SAAK,QAAQ;AAOb,SAAK,SAAS;AASd,SAAK,mBAAmB;AAQxB,SAAK,aAAa;AAQlB,SAAK,aAAa;AAQlB,SAAK,QAAQ;AAQb,SAAK,aAAa;AAQlB,SAAK,YAAY;AACjB,SAAK,iBAAiB,IAAI,MAAM;AAAA,EAEjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAQ,UAAU,aAAa,YAA0C;AAExE,UAAM,eAAe,SAAS;AAC9B,aAAS,YAAY;AAErB,QAAI,eAAe;AAEnB,QAAK,KAAK,qBAAqB,MAAO;AAErC,4BAAsB,KAAK,MAAM;AAEjC,WAAK,MAAM,mBAAmB,KAAK;AAAA,IAEpC;AAEA,QAAK,KAAK,eAAe,MAAO;AAE/B,eAAS,cAAe,KAAK,cAAe;AAC5C,eAAS,cAAe,KAAK,YAAY,SAAS,cAAc,CAAE;AAAA,IAEnE;AAEA,QAAK,KAAK,eAAe,MAAO;AAE/B,sBAAgB,SAAS,cAAc;AACvC,eAAS,cAAe,KAAK,UAAW;AAAA,IAEzC;AAEA,QAAK,KAAK,cAAc,MAAO;AAE9B,eAAS,WAAW;AAAA,IAErB;AAEA,aAAS,gBAAiB,KAAK,iBAAiB,OAAO,UAAW;AAElE,QAAK,KAAK,UAAU,MAAO;AAG1B,eAAS,MAAO,SAAS,gBAAgB,SAAS,gBAAgB,SAAS,gBAAiB;AAAA,IAE7F;AAEA,aAAS,OAAQ,KAAK,OAAO,KAAK,MAAO;AAIzC,QAAK,KAAK,eAAe,MAAO;AAE/B,eAAS,cAAe,KAAK,cAAe;AAAA,IAE7C;AAEA,QAAK,KAAK,eAAe,MAAO;AAE/B,eAAS,cAAe,aAAc;AAAA,IAEvC;AAEA,QAAK,KAAK,qBAAqB,MAAO;AAErC,WAAK,MAAM,mBAAmB;AAAA,IAE/B;AAEA,aAAS,YAAY;AAAA,EAEtB;AAED;", "names": []}